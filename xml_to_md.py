from lxml import etree
from pathlib import Path
import markdownify
 
def extract_title(tree):
    title_el = tree.find(".//article-title")
    return f"# {title_el.text.strip()}" if title_el is not None and title_el.text else ""
 
def extract_abstract(tree):
    abs_el = tree.find(".//abstract")
    if abs_el is not None:
        paragraphs = []
        for p in abs_el.xpath(".//p"):
            text = etree.tostring(p, encoding="unicode", method="html")
            text = markdownify.markdownify(text)
            if text.strip():
                paragraphs.append(f"<!-- PARAGRAPH_START [abstract] -->{text.strip()}")
        abs_text = "\n\n".join(paragraphs)
        return f"## Abstract\n{abs_text}"
    return ""
 
def compare_paragraph_counts(tree):
    """Compare paragraph counts using different methods."""
    # Method 1: Count all <p> elements
    all_p_elements = tree.xpath("//p")
    print(f"All <p> elements: {len(all_p_elements)}")
   
    # Method 2: Count non-empty <p> elements (has text)
    non_empty_p = [p for p in all_p_elements if (p.text or "").strip()]
    print(f"Non-empty <p> elements: {len(non_empty_p)}")
   
    # Method 3: Count <p> elements with any content (text or children)
    content_p = [p for p in all_p_elements if ((p.text or "").strip() or len(p.getchildren()) > 0)]
    print(f"<p> elements with content: {len(content_p)}")
   
    # Method 4: Count paragraphs as in evaluation function
    para_tags = [".//p", ".//para"]
    para_elements = []
    for tag in para_tags:
        para_elements.extend(tree.xpath(tag))
    num_xml_paragraphs = sum(1 for el in para_elements if (el.text or "").strip())
    print(f"Paragraphs counted in evaluation: {num_xml_paragraphs}")
   
    # Detailed breakdown by location
    print("\nParagraph breakdown by location:")
    print(f"- In abstract: {len(tree.xpath('//abstract//p'))}")
    print(f"- Directly in sections: {len([p for sec in tree.xpath('//sec') for p in sec.xpath('./p')])}")
    print(f"- In nested elements within sections: {len([p for sec in tree.xpath('//sec') for el in sec.xpath('./*[not(self::sec) and not(self::p) and not(self::title)]') for p in el.xpath('.//p')])}")
    print(f"- Outside sections and abstract: {len(tree.xpath('//p[not(ancestor::sec) and not(ancestor::abstract)]'))}")
   
    # Check for paragraphs that might be counted differently
    print("\nPotentially problematic paragraphs:")
   
    # Empty paragraphs with child elements
    empty_with_children = [p for p in all_p_elements if not (p.text or "").strip() and len(p.getchildren()) > 0]
    print(f"- Empty <p> with child elements: {len(empty_with_children)}")
    if empty_with_children:
        for p in empty_with_children[:3]:  # Show first 3 examples
            print(f"  Example: {etree.tostring(p, encoding='unicode', method='html')[:100]}...")
   
    # Paragraphs with only whitespace text but child elements
    whitespace_with_children = [p for p in all_p_elements if (p.text or "").strip() == "" and len(p.getchildren()) > 0]
    print(f"- Whitespace <p> with child elements: {len(whitespace_with_children)}")
   
    # Paragraphs with no text content but child elements that have text
    no_text_but_children_with_text = []
    for p in all_p_elements:
        if not (p.text or "").strip():
            child_has_text = False
            for child in p.iterdescendants():
                if (child.text or "").strip() or (child.tail or "").strip():
                    child_has_text = True
                    break
            if child_has_text:
                no_text_but_children_with_text.append(p)
   
    print(f"- <p> with no direct text but children with text: {len(no_text_but_children_with_text)}")
    if no_text_but_children_with_text:
        for p in no_text_but_children_with_text[:3]:  # Show first 3 examples
            print(f"  Example: {etree.tostring(p, encoding='unicode', method='html')[:100]}...")
 
def extract_structured_content(tree):
    """Extract all content with section hierarchy preserved, with source tracking."""
    result = []
   
    # Process all top-level sections
    for sec in tree.xpath("//sec[not(ancestor::sec)]"):
        section_content = process_section(sec, 2)  # Start at h2 level
        result.append(section_content)
   
    # Get any paragraphs that aren't in sections or abstract
    outside_paras = []
    for p in tree.xpath("//p[not(ancestor::sec) and not(ancestor::abstract)]"):
        text = etree.tostring(p, encoding="unicode", method="html")
        text = markdownify.markdownify(text)
        if text.strip():
            outside_paras.append(f"<!-- PARAGRAPH_START [outside] -->{text.strip()}")
   
    if outside_paras:
        result.append("## Other Content\n" + "\n\n".join(outside_paras))
   
    return "\n\n".join(result)
 
def process_section(sec, level):
    """Process a section and its descendants recursively, with source tracking."""
    result = []
   
    # Extract title
    title_el = sec.find("title")
    if title_el is not None and title_el.text:
        title = f"{'#' * level} {title_el.text.strip()}"
        result.append(title)
   
    # Extract all paragraphs directly in this section (not in subsections)
    for p in sec.xpath("./p"):
        text = etree.tostring(p, encoding="unicode", method="html")
        text = markdownify.markdownify(text)
        if text.strip():
            result.append(f"<!-- PARAGRAPH_START [direct] -->{text.strip()}")
   
    # Extract paragraphs from other elements directly in this section
    for el in sec.xpath("./*[not(self::sec) and not(self::p) and not(self::title)]"):
        for p in el.xpath(".//p"):
            text = etree.tostring(p, encoding="unicode", method="html")
            text = markdownify.markdownify(text)
            if text.strip():
                result.append(f"<!-- PARAGRAPH_START [nested] -->{text.strip()}")
   
    # Process subsections recursively
    for subsec in sec.xpath("./sec"):
        subsection_content = process_section(subsec, level + 1)
        result.append(subsection_content)
   
    return "\n\n".join(result)
 
def extract_tables(tree):
    tables = tree.findall(".//table-wrap")
    output = []
    for i, table_wrap in enumerate(tables, 1):
        caption_el = table_wrap.find(".//caption")
        caption = (
            etree.tostring(caption_el, method="text", encoding="unicode").strip()
            if caption_el is not None else f"Table {i}"
        )
        table_el = table_wrap.find(".//table")
        if table_el is None:
            output.append(f"### {caption}\n\n_Table not found or unstructured._")
            continue
        rows = []
        for row in table_el.findall(".//tr"):
            cells = [
                etree.tostring(cell, method="text", encoding="unicode").strip()
                for cell in row.findall("./td") + row.findall("./th")
            ]
            rows.append(cells)
        if not rows:
            output.append(f"### {caption}\n\n_Table empty._")
            continue
        header = "| " + " | ".join(rows[0]) + " |"
        separator = "| " + " | ".join("---" for _ in rows[0]) + " |"
        body = "\n".join("| " + " | ".join(row) + " |" for row in rows[1:])
        table_md = f"<!-- TABLE_START -->\n### {caption}\n\n{header}\n{separator}\n{body}"
        output.append(table_md)
    return "\n\n".join(output)
 
def extract_references(tree):
    refs = tree.findall(".//ref-list/ref")
    output = []
    for i, ref in enumerate(refs, 1):
        label = ref.findtext("label") or f"[{i}]"
        mixed = ref.find("mixed-citation")
        if mixed is not None:
            ref_text = etree.tostring(mixed, method="text", encoding="unicode").strip()
        else:
            parts = []
            name = ref.find(".//name")
            if name is not None:
                surname = name.findtext("surname", "")
                given = name.findtext("given-names", "")
                parts.append(f"{surname} {given}")
            parts.append(ref.findtext(".//article-title", ""))
            parts.append(ref.findtext(".//source", ""))
            year = ref.findtext(".//year")
            if year:
                parts.append(f"({year})")
            ref_text = ". ".join(p for p in parts if p)
        output.append(f"- {label} {ref_text}")
    return "## References\n" + "\n".join(output) if output else ""
 
def analyze_markdown_paragraphs(md_path):
    """Analyze the sources of paragraphs in the Markdown file."""
    md_text = md_path.read_text(encoding="utf-8")
   
    # Count paragraphs by source
    abstract_paras = md_text.count("<!-- PARAGRAPH_START [abstract] -->")
    direct_paras = md_text.count("<!-- PARAGRAPH_START [direct] -->")
    nested_paras = md_text.count("<!-- PARAGRAPH_START [nested] -->")
    outside_paras = md_text.count("<!-- PARAGRAPH_START [outside] -->")
   
    print("\nParagraph sources in Markdown:")
    print(f"Abstract paragraphs: {abstract_paras}")
    print(f"Direct section paragraphs: {direct_paras}")
    print(f"Nested element paragraphs: {nested_paras}")
    print(f"Outside paragraphs: {outside_paras}")
    print(f"Total paragraphs: {abstract_paras + direct_paras + nested_paras + outside_paras}")
 
def evaluate_text_preservation(xml_path, markdown_path):
    xml_tree = etree.parse(xml_path)
    # Accept both <p> and <para> tags; you can add more if your XML uses others
    para_tags = [".//p", ".//para"]
    para_elements = []
    for tag in para_tags:
        para_elements.extend(xml_tree.findall(tag))
    # Only count non-empty textual paragraphs
    num_xml_paragraphs = sum(1 for el in para_elements if (el.text or "").strip())
 
    md_text = markdown_path.read_text(encoding="utf-8")
    num_md_paragraphs = md_text.count('<!-- PARAGRAPH_START')
 
    print(f"\n🧪 Text Preservation Evaluation for {xml_path.name}:")
    print(f"📝 Paragraphs in XML:      {num_xml_paragraphs}")
    print(f"📄 Paragraphs in Markdown: {num_md_paragraphs}")
 
    if num_xml_paragraphs == 0:
        print("⚠️ No non-empty paragraphs found in XML.")
    elif abs(num_xml_paragraphs - num_md_paragraphs) <= 2:
        print("✅ Most paragraphs preserved!")
    else:
        print(f"⚠️ Difference detected: {abs(num_xml_paragraphs - num_md_paragraphs)} paragraphs.")
 
def evaluate_table_preservation(xml_path, markdown_path):
    xml_tree = etree.parse(xml_path)
    xml_table_count = len(xml_tree.findall(".//table-wrap"))
    md_text = markdown_path.read_text(encoding="utf-8")
    md_table_count = md_text.count("<!-- TABLE_START -->")
    print(f"\n🧪 Table Preservation Evaluation for {xml_path.name}:")
    print(f"🧾 Tables in XML:      {xml_table_count}")
    print(f"📄 Tables in Markdown: {md_table_count}")
    if xml_table_count == 0:
        print("⚠️ No tables in XML.")
    elif md_table_count == xml_table_count:
        print("✅ All tables preserved!")
    else:
        print(f"⚠️ {xml_table_count - md_table_count} table(s) missing in Markdown.")
 
def convert_xml_to_md(xml_path, md_path):
    tree = etree.parse(xml_path)
    root = tree.getroot()
   
    # Compare paragraph counts
    print("\n=== Paragraph Count Comparison ===")
    compare_paragraph_counts(root)
   
    # Extract content
    title = extract_title(root)
    abstract = extract_abstract(root)
    structured_content = extract_structured_content(root)
    tables = extract_tables(root)
    references = extract_references(root)
   
    # Combine all parts
    parts = [title, abstract, structured_content, tables, references]
    md_content = "\n\n".join(filter(None, parts)).strip()
    md_path.write_text(md_content, encoding="utf-8")
    print(f"\n✅ Saved: {md_path}")
   
    # Analyze markdown paragraphs
    analyze_markdown_paragraphs(md_path)
   
    evaluate_text_preservation(xml_path, md_path)
    evaluate_table_preservation(xml_path, md_path)
 
def main():
    input_folder = Path("/xml_from_parquet")
    output_folder = Path("/xml_from_parquet")
    output_folder.mkdir(exist_ok=True)
    xml_files = list(input_folder.glob("*.xml"))
    if not xml_files:
        print("No XML files found in xml_files folder.")
        return
    for xml_file in xml_files:
        md_file = output_folder / (xml_file.stem + ".md")
        try:
            convert_xml_to_md(xml_file, md_file)
        except Exception as e:
            print(f"❌ Failed to process {xml_file.name}: {e}")
 
if __name__ == "__main__":
    main()