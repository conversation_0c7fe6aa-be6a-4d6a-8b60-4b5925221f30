#!/usr/bin/env python3
import pandas as pd
import os
from pathlib import Path

def check_parquet_for_target_ids():
    """Check top_europepmc.parquet for target W IDs and extract XML content if found"""
    
    # Target W IDs from the markdown search
    target_w_ids = [
        'W4390743855', 'W2314772071', 'W4226170358', 'W4376958886',
        'W4226097966', 'W3093537091', 'W4280631979', 'W4284897854',
        'W4400124600', 'W4387230410', 'W2911746982'
    ]
    
    # Check both parquet files
    parquet_files = [
        '/home/<USER>/scp/adc_data_center/top_europepmc.parquet',
        '/home/<USER>/scp/dry_run_updated/top_europepmc.parquet'
    ]
    
    print("="*60)
    print("SEARCHING TOP_EUROPEPMC.PARQUET FOR TARGET W IDs")
    print("="*60)
    
    all_found_ids = []
    
    for parquet_file in parquet_files:
        if not os.path.exists(parquet_file):
            print(f"❌ File not found: {parquet_file}")
            continue
            
        print(f"\n📁 Checking: {parquet_file}")
        
        try:
            # Read the parquet file
            df = pd.read_parquet(parquet_file)
            print(f"   Shape: {df.shape}")
            print(f"   Columns with 'id': {[col for col in df.columns if 'id' in col.lower()]}")
            
            # Check if oa_works_id column exists
            if 'oa_works_id' not in df.columns:
                print(f"   ❌ Column 'oa_works_id' not found!")
                continue
            
            print(f"   ✅ Found 'oa_works_id' column with {df['oa_works_id'].notna().sum()} non-null values")
            
            # Search for each target ID
            found_in_this_file = []
            missing_in_this_file = []
            
            for target_id in target_w_ids:
                if target_id in df['oa_works_id'].values:
                    found_in_this_file.append(target_id)
                    all_found_ids.append(target_id)
                    print(f"   ✅ FOUND: {target_id}")
                else:
                    missing_in_this_file.append(target_id)
            
            print(f"\n   📊 Results for {os.path.basename(parquet_file)}:")
            print(f"      Found: {len(found_in_this_file)}")
            print(f"      Missing: {len(missing_in_this_file)}")
            
            if found_in_this_file:
                print(f"      Found IDs: {found_in_this_file}")
                
                # Extract XML content for found IDs
                if 'rawContent' in df.columns:
                    print(f"\n   📄 Extracting XML content for found IDs...")
                    output_dir = Path('/home/<USER>/scp/xml_from_parquet')
                    output_dir.mkdir(exist_ok=True)
                    
                    for found_id in found_in_this_file:
                        row = df[df['oa_works_id'] == found_id].iloc[0]
                        if pd.notna(row['rawContent']) and row['rawContent'].strip():
                            xml_file = output_dir / f"{found_id}.xml"
                            with open(xml_file, 'w', encoding='utf-8') as f:
                                f.write(row['rawContent'])
                            print(f"      ✅ Saved XML: {xml_file.name}")
                        else:
                            print(f"      ❌ No XML content for: {found_id}")
                else:
                    print(f"   ❌ No 'rawContent' column found for XML extraction")
            
        except Exception as e:
            print(f"   ❌ Error reading {parquet_file}: {e}")
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"FINAL SUMMARY")
    print(f"{'='*60}")
    print(f"Total target IDs: {len(target_w_ids)}")
    print(f"Found across all files: {len(set(all_found_ids))}")
    print(f"Missing: {len(target_w_ids) - len(set(all_found_ids))}")
    
    unique_found = list(set(all_found_ids))
    missing_ids = [tid for tid in target_w_ids if tid not in unique_found]
    
    if unique_found:
        print(f"\n✅ Found W IDs:")
        for found_id in sorted(unique_found):
            print(f"   - {found_id}")
    
    if missing_ids:
        print(f"\n❌ Missing W IDs:")
        for missing_id in sorted(missing_ids):
            print(f"   - {missing_id}")
    
    # Show sample of what IDs are actually in the parquet
    print(f"\n📋 Sample W IDs actually in parquet files:")
    for parquet_file in parquet_files:
        if os.path.exists(parquet_file):
            df = pd.read_parquet(parquet_file)
            if 'oa_works_id' in df.columns:
                sample_ids = df['oa_works_id'].dropna().head(10).tolist()
                print(f"   {os.path.basename(parquet_file)}: {sample_ids[:5]}...")

if __name__ == "__main__":
    check_parquet_for_target_ids()
