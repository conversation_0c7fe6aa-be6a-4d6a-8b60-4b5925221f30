"""
Refactored main CLI module with dependency injection and improved error handling.

This module provides a clean CLI interface with proper dependency injection,
comprehensive error handling, and configurable components.

TEST FILES: tests/test_main.py, tests/test_refactored_code.py, tests/test_gt_removal.py, tests/test_integration_real_api.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test files
are updated or added to maintain test coverage and validate the changes.
"""

import argparse
import sys
import os
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import partial
from typing import Optional, List, Dict, Any
from tqdm.asyncio import tqdm as aio_tqdm
from .evaluator import ADCEvaluator
from .matcher import ADCMatcher
from .llm_client import OpenAILLMClient
from openai import AzureOpenAI
from .config import Config
from .data_loader import FileDataLoader
from .results_writer import JSONResultsWriter, ConsoleResultsWriter, MultiResultsWriter
from .exceptions import (
    DataLoadingError, 
    PaperNotFoundError, 
    <PERSON><PERSON><PERSON>r,
    LLMError
)
from .utils.logging_utils import setup_logging, get_logger
from .utils.constants import Constants

class ADCEvaluationApp:
    """Main application class with dependency injection."""
    
    def __init__(
        self,
        data_loader: Optional[FileDataLoader] = None,
        evaluator: Optional[ADCEvaluator] = None,
        results_writer: Optional[MultiResultsWriter] = None,
        confidence_threshold: float = Constants.DEFAULT_CONFIDENCE_THRESHOLD,
        enable_incremental_writing: bool = True
    ):
        """Initialize the application with dependencies."""
        azure_client = AzureOpenAI(
            azure_endpoint=Config.AZURE_OPENAI_ENDPOINT,
            api_key=Config.AZURE_OPENAI_KEY,
            api_version=Config.AZURE_OPENAI_VERSION
        )
        llm_client = OpenAILLMClient(client=azure_client)
        
        matcher = ADCMatcher(llm_client)
        
        self.data_loader = data_loader or FileDataLoader()
        
        # Set up incremental writer if enabled
        incremental_writer = None
        if enable_incremental_writing:
            from .results_writer import JSONResultsWriter
            incremental_writer = JSONResultsWriter()
        
        self.evaluator = evaluator or ADCEvaluator(matcher, confidence_threshold, incremental_writer)
        if self.evaluator.matcher.llm_client is None:
            self.evaluator.matcher.llm_client = llm_client
            
        self.results_writer = results_writer or MultiResultsWriter()
        
        get_logger(__name__).info(f"Initialized ADC evaluation app with confidence threshold: {confidence_threshold}")

    def run_evaluation(self, paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR, treat_additional_correct_as_incorrect: bool = True) -> Dict[str, Any]:
        """Run evaluation for a specific paper."""
        logger = get_logger(__name__)
        try:
            logger.info(f"Starting evaluation for paper: {paper_id}")
            if hasattr(self.data_loader, 'data_dir'):
                self.data_loader.data_dir = data_dir
            
            # Update incremental writer data directory if it exists
            if hasattr(self.evaluator, 'incremental_writer') and self.evaluator.incremental_writer:
                self.evaluator.incremental_writer.data_dir = data_dir
            
            gt_doc, extraction_rows = self.data_loader.load_paper_data(paper_id)
            results = self.evaluator.evaluate(extraction_rows, gt_doc, treat_additional_correct_as_incorrect)
            logger.info(f"Evaluation completed for paper {paper_id}")
            return results
        except Exception as e:
            logger.error(f"Evaluation failed for paper {paper_id}: {str(e)}")
            raise

    async def run_all_evaluations_async(self, data_dir: str, max_workers: int, paper_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Run evaluation for all papers or specified papers in parallel."""
        logger = get_logger(__name__)
        
        if paper_ids is None:
            # Get all papers from extraction_results directory
            extraction_dir = os.path.join(data_dir, "extraction_results")
            if not os.path.isdir(extraction_dir):
                logger.error(f"Extraction results directory not found: {extraction_dir}")
                return []
            paper_ids = [f.replace("_results.json", "") for f in os.listdir(extraction_dir) if f.endswith("_results.json")]
        
        # Filter out already evaluated papers
        evaluations_dir = os.path.join(data_dir, "evaluations")
        if os.path.isdir(evaluations_dir):
            evaluated_papers = {f.replace("_evaluation.json", "").upper() for f in os.listdir(evaluations_dir) if f.endswith("_evaluation.json")}
            original_count = len(paper_ids)
            paper_ids = [p for p in paper_ids if p.upper() not in evaluated_papers]
            if original_count > len(paper_ids):
                logger.info(f"Skipping {original_count - len(paper_ids)} already evaluated papers")
        
        if not paper_ids:
            logger.info("No papers to evaluate (all papers already evaluated)")
            return []
        
        all_results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            loop = asyncio.get_event_loop()
            
            # Create a partial function with fixed arguments
            run_eval_partial = partial(self.run_evaluation, data_dir=data_dir)

            # Create a list of futures
            futures = [loop.run_in_executor(executor, run_eval_partial, paper_id) for paper_id in paper_ids]
            
            for future in aio_tqdm.as_completed(futures, total=len(futures), desc="Evaluating all papers"):
                try:
                    results = await future
                    self.save_results(results)
                    all_results.append(results)
                except Exception as e:
                    logger.error(f"Failed to evaluate paper: {e}")

        return all_results

    def save_results(self, results: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Save evaluation results."""
        logger = get_logger(__name__)
        try:
            saved_path = self.results_writer.write_results(results, output_path)
            logger.info(f"Results saved to: {saved_path}")
            return saved_path
        except Exception as e:
            logger.error(f"Failed to save results: {str(e)}")
            raise

def create_parser() -> argparse.ArgumentParser:
    """Create and return the argument parser."""
    parser = argparse.ArgumentParser(
        description="Evaluate ADC extraction results per paper.",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--paper-id", help="Paper ID to evaluate (e.g., w2314772071).")
    group.add_argument("--all-papers", action="store_true", help="Evaluate all papers in the data directory.")
    group.add_argument("--paper-ids", nargs="+", help="List of paper IDs to evaluate in parallel (e.g., w2314772071 w2778226119).")
    
    parser.add_argument("--data-dir", default=Constants.DEFAULT_DATA_DIR, help=f"Base data directory (default: {Constants.DEFAULT_DATA_DIR}).")
    parser.add_argument("--confidence-threshold", type=float, default=Constants.DEFAULT_CONFIDENCE_THRESHOLD, help=f"Confidence threshold for TP classification (default: {Constants.DEFAULT_CONFIDENCE_THRESHOLD}).")
    parser.add_argument("--output-path", help="Custom output path for results (optional).")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging.")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress console output (only save to file).")
    parser.add_argument("--log-file", help="Path to log file (optional).")
    parser.add_argument("--parallel-workers", type=int, default=1, help="Number of papers to evaluate in parallel (default: 1).")
    parser.add_argument("--allow-additional-correct", action="store_true", help="Treat extractions marked as 'Correct' and 'Additional' as 'Correct' for evaluation. Default is to treat them as 'Incorrect'.")
    parser.add_argument("--priority-filter", choices=['all', 'prioritized_and_proxied'], default='all', help="Filter endpoints by priority: 'all' for no filtering, 'prioritized_and_proxied' for only Prioritize and Proxied endpoints (default: all).")
    
    return parser

def configure_logging(verbose: bool, log_file: Optional[str] = None) -> None:
    """Configure logging based on arguments."""
    import logging
    level = logging.DEBUG if verbose else logging.INFO
    setup_logging(level=level, log_file=log_file)

def handle_error(error: Exception, logger) -> int:
    """Handle errors and return appropriate exit codes."""
    if isinstance(error, PaperNotFoundError):
        logger.error(f"Paper not found: {error}")
        return 2
    elif isinstance(error, DataLoadingError):
        logger.error(f"Data loading error: {error}")
        return 3
    elif isinstance(error, EvaluationError):
        logger.error(f"Evaluation error: {error}")
        return 4
    elif isinstance(error, LLMError):
        logger.error(f"LLM error: {error}")
        return 5
    else:
        logger.error(f"Unexpected error: {error}")
        return 1

def main() -> int:
    """Main function."""
    parser = create_parser()
    args = parser.parse_args()
    
    configure_logging(args.verbose, args.log_file)
    logger = get_logger(__name__)
    
    try:
        writers = []
        if not args.quiet:
            writers.append(ConsoleResultsWriter(verbose=args.verbose))
        writers.append(JSONResultsWriter(args.data_dir))
        
        # Create data loader with priority filter
        data_loader = FileDataLoader(args.data_dir, priority_filter=args.priority_filter)
        
        app = ADCEvaluationApp(
            data_loader=data_loader,
            results_writer=MultiResultsWriter(writers),
            confidence_threshold=args.confidence_threshold
        )
        
        if args.all_papers:
            logger.info(f"Starting evaluation for all papers with {args.parallel_workers} parallel workers.")
            all_results = asyncio.run(app.run_all_evaluations_async(args.data_dir, args.parallel_workers))
            logger.info(f"Completed evaluation for {len(all_results)} papers.")
        elif args.paper_ids:
            logger.info(f"Starting evaluation for {len(args.paper_ids)} specified papers with {args.parallel_workers} parallel workers.")
            all_results = asyncio.run(app.run_all_evaluations_async(args.data_dir, args.parallel_workers, args.paper_ids))
            logger.info(f"Completed evaluation for {len(all_results)} papers.")
        else:
            logger.info(f"Starting ADC evaluation for paper: {args.paper_id}")
            results = app.run_evaluation(
                args.paper_id, 
                args.data_dir,
                treat_additional_correct_as_incorrect=not args.allow_additional_correct
            )
            output_path = app.save_results(results, args.output_path)
            if not args.quiet:
                print(f"\nEvaluation completed successfully!")
                print(f"Results: TP={results['TP']}, FP={results['FP']}, FN={results['FN']}")
                print(f"F1 Score: {results['f1']:.3f}")
                print(f"Saved to: {output_path}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Evaluation interrupted by user")
        return 130
        
    except Exception as e:
        return handle_error(e, logger)

if __name__ == "__main__":
    sys.exit(main())