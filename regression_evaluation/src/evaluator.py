"""
Improved ADC evaluator with better separation of concerns and error handling.

This module implements the Evaluator interface with proper error handling,
logging, dependency injection, and metrics calculation.

TEST FILE: tests/test_evaluator.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test file
are updated or added to maintain test coverage and validate the changes.
"""

import math
from collections import defaultdict
from tqdm import tqdm
from typing import List, Optional, Set, Dict, Any, Tuple
from .models import *
from .interfaces import <PERSON><PERSON><PERSON>, Matcher
from .matcher import ADCMatcher
from .exceptions import EvaluationError, MatchingError
from .utils.logging_utils import LoggerMixin, log_async_function_call
from .utils.constants import Constants


class ADCEvaluator(LoggerMixin):
    """Evaluates ADC extraction results against ground truth."""
    
    def __init__(self, matcher: any, confidence_threshold: float = Constants.DEFAULT_CONFIDENCE_THRESHOLD, incremental_writer=None):
        """Initialize the evaluator with a matcher and optional incremental writer."""
        self.matcher = ADCMatcher()
        self.confidence_threshold = confidence_threshold
        self.incremental_writer = incremental_writer
        self.logger.info(f"Initialized ADC evaluator with confidence threshold: {self.confidence_threshold}")
        if incremental_writer:
            self.logger.info("Incremental writing enabled")
    
    def evaluate(self, extraction_results: List[Tuple[ExtractedEndpoint, Dict[str, Any]]], ground_truth: Dict[str, Any], treat_additional_correct_as_incorrect: bool = False) -> Dict[str, Any]:
        """Evaluate extraction results against ground truth."""
        try:
            paper_id = ground_truth.get('id', ground_truth.get('paper_id', 'unknown'))
            gt_records = ground_truth.get('records', ground_truth.get('endpoints', []))
            self.logger.info(f"Starting evaluation for paper {paper_id}: {len(extraction_results)} extractions vs {len(gt_records)} ground truth records")
            
            if treat_additional_correct_as_incorrect:
                self.logger.info("Flag 'treat_additional_correct_as_incorrect' is enabled.")
                count = 0
                for _, raw_dict in extraction_results:
                    if raw_dict.get('source') == "Additional" and raw_dict.get('status') == "Correct":
                        raw_dict['status'] = "Incorrect"
                        count += 1
                if count > 0:
                    self.logger.info(f"Reclassified {count} 'Additional' and 'Correct' extractions to 'Incorrect'.")

            # Initialize evaluation state with available GT records
            evaluation_state = self._initialize_evaluation_state(paper_id)
            available_gt_records = [GTEndpoint(**rec) for rec in gt_records]

            # Assign persistent GT IDs once at the start of evaluation
            for i, gt_record in enumerate(available_gt_records):
                gt_record.gt_id = f"GT_ROW_{i+1}"
            
            self.logger.debug(f"Assigned GT IDs to {len(available_gt_records)} records: GT_ROW_1 through GT_ROW_{len(available_gt_records)}")
            
            # Initialize incremental file if incremental writer is available
            if self.incremental_writer:
                # Safely convert GT records for initial data
                initial_gt_items = []
                for gt in available_gt_records:
                    if hasattr(gt, 'model_dump'):
                        initial_gt_items.append(gt.model_dump())
                    elif isinstance(gt, dict):
                        initial_gt_items.append(gt)
                    else:
                        initial_gt_items.append({"error": f"Unexpected type: {type(gt)}"})
                
                initial_data = {
                    'total_extractions': len(extraction_results),
                    'total_ground_truth': len(available_gt_records),
                    'unmatched_gt_items': initial_gt_items
                }
                self.incremental_writer.initialize_incremental_file(paper_id, initial_data)
            
            # Process each extraction record with dynamic GT removal
            for idx, (extracted_endpoint, raw_data) in enumerate(tqdm(extraction_results, desc="Evaluating Records")):
                self._process_extraction_endpoint_with_removal(
                    extracted_endpoint, raw_data, available_gt_records, idx, evaluation_state
                )
                
                # Update incremental file after each extraction
                if self.incremental_writer:
                    self.logger.info(f"Updating incremental progress for extraction {idx + 1}/{len(extraction_results)}")
                    self._update_incremental_progress(paper_id, evaluation_state, available_gt_records, idx + 1)
            
            # Calculate final metrics with remaining unmatched GT endpoints
            final_results = self._calculate_final_metrics_with_remaining(evaluation_state, available_gt_records)
            
            # Finalize incremental file if incremental writer is available
            if self.incremental_writer:
                self.incremental_writer.finalize_incremental_file(paper_id, final_results)
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {str(e)}")
            raise EvaluationError(f"Evaluation failed: {str(e)}")
    
    def _validate_inputs(self, extraction_results: List[ExtractionRow], ground_truth: GroundTruthDocument) -> None:
        """Validate evaluation inputs."""
        if not extraction_results:
            raise EvaluationError("Extraction results cannot be empty")
        
        if not ground_truth:
            raise EvaluationError("Ground truth document cannot be None")
        
        if not ground_truth.endpoints:
            raise EvaluationError("Ground truth must contain at least one endpoint")
        
        if not ground_truth.paper_id:
            raise EvaluationError("Ground truth must have a paper_id")
    
    def _initialize_evaluation_state(self, paper_id: str) -> Dict[str, Any]:
        """Initialize evaluation state tracking."""
        return {
            "paper_id": paper_id,
            "TP": 0,
            "FP": 0,
            "matched_gt_endpoints": set(),
            "detailed_results": []
        }
    
    def _validate_extraction_record(self, record: Dict[str, Any], idx: int) -> None:
        """Validate extraction record has all required fields with non-null values."""
        required_fields = ['adc_name', 'model_name', 'endpoint_name']
        critical_fields = ['endpoint_citations']  # Critical for proper matching
        
        # Check for paper ID (could be 'id' or 'paper_id')
        if not record.get('id') and not record.get('paper_id'):
            raise ValueError(f"Extraction record {idx + 1} missing both 'id' and 'paper_id' fields")
        
        # Check required fields
        for field in required_fields:
            if field not in record:
                raise ValueError(f"Extraction record {idx + 1} missing required field: '{field}'")
            if record[field] is None or record[field] == '':
                raise ValueError(f"Extraction record {idx + 1} has null/empty value for required field: '{field}'")
        
        # Check critical fields (warn but don't fail)
        for field in critical_fields:
            if field not in record:
                self.logger.warning(f"Extraction record {idx + 1} missing critical field: '{field}' - matching accuracy may be affected")
            elif record[field] is None or record[field] == '':
                self.logger.warning(f"Extraction record {idx + 1} has null/empty value for critical field: '{field}' - matching accuracy may be affected")
    
    def _validate_gt_record(self, record: Dict[str, Any], idx: int) -> None:
        """Validate ground truth record has all required fields with non-null values."""
        required_fields = ['adc_name', 'model_name', 'endpoint_name', 'gt_id']
        
        # Check for paper ID (could be 'id' or 'paper_id')
        if not record.get('id') and not record.get('paper_id'):
            raise ValueError(f"Ground truth record {idx + 1} missing both 'id' and 'paper_id' fields")
        
        # Check required fields
        for field in required_fields:
            if field not in record:
                raise ValueError(f"Ground truth record {idx + 1} missing required field: '{field}'")
            if record[field] is None or record[field] == '':
                raise ValueError(f"Ground truth record {idx + 1} has null/empty value for required field: '{field}'")
    
    def _process_extraction_endpoint_with_removal(
        self, 
        extracted_record: ExtractedEndpoint, 
        raw_data: Dict[str, Any],
        available_gt_records: List[GTEndpoint], 
        idx: int,
        evaluation_state: Dict[str, Any]
    ) -> None:
        """Process a single extraction record and remove matched GT records from available pool."""
        try:
            endpoint_name = extracted_record.endpoint_name
            self.logger.debug(f"Processing extraction record {idx + 1}: {endpoint_name} against {len(available_gt_records)} available GT records")
            
            match_result = self.matcher.match_endpoint(extracted_record, available_gt_records)
            
            # Determine classification and handle GT removal
            classification = self._classify_and_remove_matched_gt(
                match_result, available_gt_records, evaluation_state
            )
            
            # Store detailed result with Phase 5 structure (preserve original extraction structure)
            result_dict = {
                "extraction_row": raw_data,
                "llm_response": match_result.model_dump(),
                "human_classification": extracted_record.status,
                "human_reasoning": extracted_record.comments,
                "comments": extracted_record.comments
            }
            evaluation_state["detailed_results"].append(result_dict)
            
            self.logger.debug(f"Endpoint {idx + 1} classified as {classification} (confidence: {match_result.confidence}). {len(available_gt_records)} GT endpoints remaining.")
            
        except Exception as e:
            self.logger.error(f"Error processing extraction endpoint {idx + 1}: {str(e)}")
            # Continue processing other endpoints, but log the error
            # Store error result with Phase 5 structure
            error_result = {
                "extraction_row": raw_data,
                "llm_response": {
                    "matched": False,
                    "confidence": 0.0,
                    "reason": f"Processing error: {str(e)}"
                },
                "human_classification": extracted_record.status,
                "human_reasoning": extracted_record.comments,
                "comments": extracted_record.comments
            }
            evaluation_state["detailed_results"].append(error_result)
            evaluation_state["FP"] += 1
    
    def _classify_and_remove_matched_gt(
        self, 
        match_result: LLMMatchResponse, 
        available_gt_records: List[GTEndpoint], 
        evaluation_state: Dict[str, Any]
    ) -> str:
        """Classify match result and remove matched GT endpoint from available pool."""
        if match_result.matched and match_result.confidence >= self.confidence_threshold:
            # This is a True Positive - remove the matched GT record using gt_id
            if match_result.gt_id:
                # Find the dictionary record with matching gt_id
                matched_record = None
                for gt_record in available_gt_records:
                    gt_id = gt_record.get('gt_id') if isinstance(gt_record, dict) else gt_record.gt_id
                    if gt_id == match_result.gt_id:
                        matched_record = gt_record
                        break
                
                if matched_record:
                    available_gt_records.remove(matched_record)
                    adc_name = matched_record.get('adc_name', 'unknown') if isinstance(matched_record, dict) else matched_record.adc_name
                    model_name = matched_record.get('model_name', 'unknown') if isinstance(matched_record, dict) else matched_record.model_name
                    endpoint_name = matched_record.get('endpoint_name', 'unknown') if isinstance(matched_record, dict) else matched_record.endpoint_name
                    self.logger.debug(f"Removed matched GT record: {adc_name}/{model_name}/{endpoint_name}")
                    evaluation_state["TP"] += 1
                    evaluation_state["matched_gt_endpoints"].add(match_result.gt_id)
                    return Constants.TRUE_POSITIVE
                else:
                    self.logger.warning(f"Could not remove GT endpoint from available pool - endpoint not found")
                    evaluation_state["FP"] += 1
                    return Constants.FALSE_POSITIVE
            else:
                self.logger.warning(f"TP classification but no gt_id provided")
                evaluation_state["FP"] += 1
                return Constants.FALSE_POSITIVE
        else:
            # This is a False Positive - extraction doesn't match any available GT
            evaluation_state["FP"] += 1
            return Constants.FALSE_POSITIVE
    
    def _calculate_agreement_score(self, x: List[int], y: List[int]) -> Optional[float]:
        """Calculate the agreement score between two binary vectors."""
        if not x or not y:
            return None
        
        agreements = sum(1 for i in range(len(x)) if x[i] == y[i])
        disagreements = len(x) - agreements
        
        return (agreements - disagreements) / len(x)

    def _calculate_correlation_metrics(self, detailed_results: List[MatchResult]) -> (Optional[float], Dict[str, Optional[float]]):
        """Calculate overall and per-endpoint agreement scores."""
        eval_bits = []
        human_bits = []
        per_ep = defaultdict(lambda: {"eval": [], "human": []})

        for row in detailed_results:
            # Skip non-dictionary rows
            if not isinstance(row, dict):
                self.logger.warning(f"Skipping non-dict row in correlation calculation: {type(row)}")
                continue
                
            # Determine LLM classification from llm_response
            llm_response = row.get("llm_response", {})
            if not isinstance(llm_response, dict):
                llm_response = {}
            llm_matched = llm_response.get("matched", False)
            llm_confidence = llm_response.get("confidence", 0.0)
            eval_bit = 1 if llm_matched and llm_confidence >= self.confidence_threshold else 0
            
            # Handle human annotations using standardized field
            if row.get("human_classification"):
                # Use standardized human_classification field (TP/FP format)
                human_bit = 1 if row["human_classification"] == "TP" else 0
            else:
                # If no human classification available, set to 0 (treat as FP for correlation)
                human_bit = 0
            
            eval_bits.append(eval_bit)
            human_bits.append(human_bit)

            # Use .get() to avoid errors if 'endpoint_name' is missing
            ep = row["extraction_row"].get("endpoint_name")
            if ep:
                per_ep[ep]["eval"].append(eval_bit)
                per_ep[ep]["human"].append(human_bit)

        overall_score = self._calculate_agreement_score(eval_bits, human_bits)
        per_ep_score = {ep: self._calculate_agreement_score(v["eval"], v["human"]) for ep, v in per_ep.items()}
        
        return overall_score, per_ep_score

    def _calculate_final_metrics_with_remaining(self, evaluation_state: Dict[str, Any], remaining_gt_endpoints: List[GTEndpoint]) -> EvaluationResult:
        """Calculate final evaluation metrics using remaining unmatched GT endpoints."""
        TP = evaluation_state["TP"]
        FP = evaluation_state["FP"]
        
        # FN = remaining unmatched GT endpoints
        FN = len(remaining_gt_endpoints)
        
        # Convert remaining GT endpoints to unmatched items format
        unmatched_gt_items = []
        for endpoint in remaining_gt_endpoints:
            if hasattr(endpoint, 'model_dump'):
                endpoint_dict = endpoint.model_dump()
            else:
                endpoint_dict = dict(endpoint) if isinstance(endpoint, dict) else endpoint
            endpoint_dict["type"] = Constants.ROW_TYPE_ENDPOINT
            unmatched_gt_items.append(endpoint_dict)
        
        # Calculate metrics with proper rounding
        precision = round(TP / (TP + FP), Constants.PRECISION_DECIMALS) if (TP + FP) > 0 else 0.0
        recall = round(TP / (TP + FN), Constants.RECALL_DECIMALS) if (TP + FN) > 0 else 0.0
        f1 = round(2 * (precision * recall) / (precision + recall), Constants.F1_DECIMALS) if (precision + recall) > 0 else 0.0
        
        # Calculate correlation metrics
        overall_corr, per_ep_corr = self._calculate_correlation_metrics(evaluation_state["detailed_results"])

        # Log summary metrics
        self.logger.info(f"Evaluation completed with GT removal: TP={TP}, FP={FP}, FN={FN}, Precision={precision}, Recall={recall}, F1={f1}")
        
        # Convert detailed_results to dictionaries if they are Pydantic models
        detailed_results_dict = []
        for result in evaluation_state["detailed_results"]:
            if hasattr(result, 'model_dump'):
                detailed_results_dict.append(result.model_dump())
            else:
                detailed_results_dict.append(result)
        
        return {
            "paper_id": evaluation_state["paper_id"],
            "TP": TP, "FP": FP, "FN": FN,
            "precision": precision, "recall": recall, "f1": f1,
            "llm_vs_human_corr_overall": overall_corr,
            "llm_vs_human_corr_by_endpoint": per_ep_corr,
            "detailed_results": detailed_results_dict,
            "unmatched_gt_items": unmatched_gt_items
        }
    
    # Legacy method for backward compatibility
    async def _process_extraction_endpoint(
        self, 
        extracted_endpoint: ExtractionRow, 
        gt_endpoints: List[GTEndpoint], 
        idx: int,
        evaluation_state: Dict[str, Any]
    ) -> None:
        """Legacy method - delegates to new implementation with removal."""
        # For backward compatibility, create a copy so original list isn't modified
        available_gt_records = list(gt_endpoints)
        await self._process_extraction_endpoint_with_removal(
            extracted_endpoint, available_gt_records, idx, evaluation_state
        )
    
    
    # Legacy method for backward compatibility
    def _classify_match_result(self, match_result: LLMMatchResponse, evaluation_state: Dict[str, Any]) -> str:
        """Legacy method - classify match result as TP or FP without GT removal."""
        if match_result.matched and match_result.confidence >= self.confidence_threshold:
            evaluation_state["TP"] += 1
            evaluation_state["matched_gt_endpoints"].add(match_result.gt_id)
            return Constants.TRUE_POSITIVE
        else:
            evaluation_state["FP"] += 1
            return Constants.FALSE_POSITIVE
    
    
    # Legacy method for backward compatibility
    def _calculate_final_metrics(self, evaluation_state: Dict[str, Any], ground_truth: GroundTruthDocument) -> EvaluationResult:
        """Legacy method - calculate final evaluation metrics without GT removal logic."""
        TP = evaluation_state["TP"]
        FP = evaluation_state["FP"]
        
        # Calculate FN: Ground truth endpoints not matched by any extraction
        total_gt_endpoints = len(ground_truth.endpoints)
        FN = total_gt_endpoints - len(evaluation_state["matched_gt_endpoints"])
        
        # Identify unmatched GT endpoints for detailed reporting
        unmatched_gt_items = self._identify_unmatched_gt_endpoints(
            ground_truth.endpoints, evaluation_state["matched_gt_endpoints"]
        )
        
        # Calculate metrics with proper rounding
        precision = round(TP / (TP + FP), Constants.PRECISION_DECIMALS) if (TP + FP) > 0 else 0.0
        recall = round(TP / (TP + FN), Constants.RECALL_DECIMALS) if (TP + FN) > 0 else 0.0
        f1 = round(2 * (precision * recall) / (precision + recall), Constants.F1_DECIMALS) if (precision + recall) > 0 else 0.0
        
        # Calculate correlation metrics
        overall_corr, per_ep_corr = self._calculate_correlation_metrics(evaluation_state["detailed_results"])

        # Log summary metrics
        self.logger.info(f"Evaluation completed: TP={TP}, FP={FP}, FN={FN}, Precision={precision}, Recall={recall}, F1={f1}")
        
        # Convert detailed_results to dictionaries if they are Pydantic models
        detailed_results_dict = []
        for result in evaluation_state["detailed_results"]:
            if hasattr(result, 'model_dump'):
                detailed_results_dict.append(result.model_dump())
            else:
                detailed_results_dict.append(result)
        
        return {
            "paper_id": evaluation_state["paper_id"],
            "TP": TP, "FP": FP, "FN": FN,
            "precision": precision, "recall": recall, "f1": f1,
            "llm_vs_human_corr_overall": overall_corr,
            "llm_vs_human_corr_by_endpoint": per_ep_corr,
            "detailed_results": detailed_results_dict,
            "unmatched_gt_items": unmatched_gt_items
        }
    
    def _update_incremental_progress(self, paper_id: str, evaluation_state: Dict[str, Any], 
                                   available_gt_records: List, completed_extractions: int) -> None:
        """Update incremental file with current progress and metrics."""
        try:
            # Calculate current metrics
            TP = evaluation_state["TP"]
            FP = evaluation_state["FP"]
            
            # Calculate FN from remaining unmatched GT records (proper sequential GT removal)
            unmatched_gt_items = []
            for gt in available_gt_records:
                if hasattr(gt, 'model_dump'):
                    unmatched_gt_items.append(gt.model_dump())
                elif isinstance(gt, dict):
                    unmatched_gt_items.append(gt)
                else:
                    self.logger.warning(f"Unexpected GT record type: {type(gt)} - converting to dict")
                    # Fallback for unexpected types
                    unmatched_gt_items.append({"error": f"Unexpected type: {type(gt)}"})
            FN = len(unmatched_gt_items)
            
            # Calculate precision, recall, f1
            precision = TP / (TP + FP) if (TP + FP) > 0 else 0.0
            recall = TP / (TP + FN) if (TP + FN) > 0 else 0.0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
            
            # Calculate correlation metrics if available
            overall_corr, per_ep_corr = self._calculate_correlation_metrics(evaluation_state)
            
            # Get the latest detailed result
            detailed_results = evaluation_state.get("detailed_results", [])
            self.logger.info(f"Incremental update: found {len(detailed_results)} detailed results in evaluation_state")
            if detailed_results:
                latest_result = detailed_results[-1]  # Get the most recently added result
                
                # Ensure latest_result is a dictionary (convert if it's a Pydantic model)
                if hasattr(latest_result, 'model_dump'):
                    latest_result = latest_result.model_dump()
                elif not isinstance(latest_result, dict):
                    self.logger.warning(f"Unexpected result type: {type(latest_result)}, skipping incremental update")
                    return
                
                # Current metrics for this update
                current_metrics = {
                    "TP": TP,
                    "FP": FP,
                    "FN": FN,
                    "precision": precision,
                    "recall": recall,
                    "f1": f1,
                    "llm_vs_human_corr_overall": overall_corr,
                    "llm_vs_human_corr_by_endpoint": per_ep_corr,
                    "unmatched_gt_items": unmatched_gt_items
                }
                
                # Update incremental file
                self.incremental_writer.update_incremental_result(paper_id, latest_result, current_metrics)
                self.logger.info(f"Successfully updated incremental file with extraction {completed_extractions}")
            else:
                self.logger.warning(f"No detailed results found for incremental update (completed_extractions: {completed_extractions})")
                
        except Exception as e:
            import traceback
            self.logger.warning(f"Failed to update incremental progress: {str(e)}")
            self.logger.warning(f"Traceback: {traceback.format_exc()}")
    
    def _identify_unmatched_gt_endpoints(self, gt_endpoints: List[GTEndpoint], matched_gt_endpoints: Set[str]) -> List[Dict[str, Any]]:
        """Identify ground truth endpoints that were not matched."""
        unmatched_gt_items = []
        
        for endpoint in gt_endpoints:
            endpoint_id = self._generate_endpoint_id(endpoint)
            if endpoint_id not in matched_gt_endpoints:
                endpoint_dict = endpoint.model_dump()
                endpoint_dict["type"] = Constants.ROW_TYPE_ENDPOINT
                unmatched_gt_items.append(endpoint_dict)
        
        self.logger.debug(f"Found {len(unmatched_gt_items)} unmatched ground truth endpoints")
        return unmatched_gt_items
    
    def _generate_endpoint_id(self, endpoint: GTEndpoint) -> str:
        """Generate a unique ID for an endpoint."""
        return f"endpoint_{endpoint.adc_name}_{endpoint.model_name}_{endpoint.endpoint_name}"
    
    def get_evaluation_summary(self, result: EvaluationResult) -> Dict[str, Any]:
        """Get a summary of evaluation results."""
        return {
            "paper_id": result.paper_id,
            "metrics": {
                "TP": result.TP,
                "FP": result.FP,
                "FN": result.FN,
                "precision": result.precision,
                "recall": result.recall,
                "f1": result.f1,
                "llm_vs_human_corr_overall": result.llm_vs_human_corr_overall,
                "llm_vs_human_corr_by_endpoint": result.llm_vs_human_corr_by_endpoint
            },
            "total_extractions": len(result.detailed_results),
            "total_gt_endpoints": result.TP + result.FN,
            "confidence_threshold": self.confidence_threshold
        }