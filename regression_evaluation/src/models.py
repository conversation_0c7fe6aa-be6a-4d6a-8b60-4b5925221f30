"""
Pydantic data models for the ADC evaluation system.

This module defines all data structures used for ground truth, extraction results,
and evaluation responses with proper validation and type safety.

TEST FILE: tests/test_models.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test file
are updated or added to maintain test coverage and validate the changes.
"""

from pydantic import BaseModel, Field, field_validator, model_validator
from typing import List, Optional, Any, Union

# Supporting Models for Endpoints
class GTDose(BaseModel):
    value: float
    unit: str
    
class GTTimePoint(BaseModel):
    value: float  
    unit: str

# Simplified Ground Truth Model - ENDPOINTS ONLY (Standardized to extraction format names)
class GTEndpoint(BaseModel):
    id: str                          # Standardized field name (was paper_id)
    adc_name: str                    # Contains ADC information
    model_name: str                  # Contains Model information  
    model_type: Optional[str] = None
    experiment_type: Optional[str] = None
    endpoint_name: str
    # Standardized field names matching extraction format (JSON1)
    measured_value: Optional[Union[float, int, str]] = None    # Was endpoint_value
    measured_timepoint: Optional[str] = None                   # Was timepoint
    measured_concentration: Optional[str] = None               # Was concentration
    measured_dose: Optional[str] = None                        # Was dose
    measured_death_percentage: Optional[str] = None            # Was death_percentage
    endpoint_units: Optional[str] = None                       # Allow None for endpoints without units
    gt_id: Optional[str] = None                                # Standardized GT ID for tracking (e.g., "GT_ROW_1")
    priority: Optional[str] = None                             # Priority from Priority column (Prioritize, Deprioritize, Proxied, Unspecified)
    # REMOVED orphaned ground truth fields:
    # - endpoint_type (orphaned in ground truth)
    # - percent_complete_response (orphaned in ground truth)  
    # - percent_partial_response (orphaned in ground truth)
    
class GroundTruthDocument(BaseModel):
    id: str  # Standardized field name (was paper_id) 
    endpoints: List[GTEndpoint]  # ONLY endpoints - contains all ADC+Model+Endpoint info
    
# Simplified Extraction Models - ENDPOINTS ONLY
class ExtractedValueObject(BaseModel):
    value: Union[float, int, str]
    unit: Optional[str] = None
    citation: str
    
class ExtractedDose(BaseModel):
    value: Union[float, int, str]
    unit: Optional[str] = None
    citation: str

class Measurement(BaseModel):
    measurement_id: str
    value: Optional[ExtractedValueObject] = None
    dose: Optional[ExtractedDose] = None
    concentration: Optional[ExtractedDose] = None
    time_point: Optional[ExtractedDose] = None
    death_percentage: Optional[ExtractedValueObject] = None
    
class ExtractedEndpoint(BaseModel):
    id: Optional[str] = None                          # Standardized field name (was paper_id)
    paper_id: Optional[str] = None                    # Legacy field for backward compatibility
    type: str = "endpoint"
    adc_name: str                    # Contains ADC information
    model_name: str                  # Contains Model information
    model_type: Optional[str] = None
    experiment_type: Optional[str] = None
    endpoint_name: str
    
    @model_validator(mode='before')
    @classmethod
    def normalize_id_field(cls, values):
        """Accept both 'id' and 'paper_id' and normalize to 'id'."""
        if isinstance(values, dict):
            # If paper_id exists but id doesn't, copy paper_id to id
            if 'paper_id' in values and 'id' not in values:
                values['id'] = values['paper_id']
            # If id exists but paper_id doesn't, copy id to paper_id for backward compatibility
            elif 'id' in values and 'paper_id' not in values:
                values['paper_id'] = values['id']
            # Ensure we have at least one
            if 'id' not in values and 'paper_id' not in values:
                raise ValueError("Either 'id' or 'paper_id' field is required")
        return values
    # Standardized extraction fields matching JSON1 format
    measured_value: Optional[str] = None         # Main measurement value
    measured_timepoint: Optional[str] = None     # Timepoint for measurement
    measured_concentration: Optional[str] = None # Concentration used
    measured_dose: Optional[str] = None          # Dose used
    measured_death_percentage: Optional[str] = None # Death percentage
    endpoint_units: Optional[str] = None         # Units for the measurement
    priority: Optional[str] = None               # Priority from Priority column (Prioritize, Deprioritize, Proxied, Unspecified)
    # Extraction-specific orphan fields (INCLUDED for evaluation and reporting)
    endpoint_citations: Optional[str] = None     # Citations for LLM matching
    human_classification: Optional[str] = None   # Human annotation for correlation analysis
    human_reasoning: Optional[str] = None        # Human reasoning for storage
    # Keep existing fields for backward compatibility during transition
    comments: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = None
    status_cat: Optional[str] = None
    # Additional field that may be present in some data  
    _row_index: Optional[int] = None
    # EXCLUDED fields: expert_reasoning, endpoint_measurements (not in actual data)
    
# Simplified - only ExtractedEndpoint now
ExtractionRow = ExtractedEndpoint

# LLM Response Model - Simplified (Phase 4.2)
class LLMMatchResponse(BaseModel):
    matched: bool
    gt_id: Optional[str] = None  # For identifying which GT item matched
    matched_gt_endpoint: Optional["GTEndpoint"] = None  # The actual matched GT endpoint for removal
    confidence: float  # 0.0 to 1.0
    reason: str
    language_model: Optional[str] = None  # Which model was used (o3-mini, gpt-4.1, etc.)
    # REMOVED per Phase 4.2: reasoning, reasoning_tokens fields
    
# Individual Match Result
class MatchResult(BaseModel):
    extraction_row: dict  # The original extraction row data
    llm_response: LLMMatchResponse  # The LLM's match response
    classification: str  # "TP", "FP", or "FN"
    extraction_row_index: int  # Index in the original extraction list
    comments: Optional[str] = None
    source: Optional[str] = None
    status: Optional[str] = None
    status_cat: Optional[str] = None

# Evaluation Results
class EvaluationResult(BaseModel):
    paper_id: str
    TP: int
    FP: int  
    FN: int
    precision: float
    recall: float
    f1: float
    llm_vs_human_corr_overall: Optional[float] = None
    llm_vs_human_corr_by_endpoint: Optional[dict] = None
    detailed_results: List[MatchResult] = []  # Detailed results for each extraction row
    unmatched_gt_items: List[dict] = []  # GT items that weren't matched (contributing to FN)