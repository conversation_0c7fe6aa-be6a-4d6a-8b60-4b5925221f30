"""
Unified extraction results converter for ADC evaluation data.

This module provides a single converter that handles both annotated (Excel) and 
unannotated (CSV) extraction results, producing consistent JSON structure regardless
of the input format. Uses the clean structure established by excel_converter.py
as the standard.

Key features:
- Single converter with --annotated flag to handle both data types
- Consistent JSON structure matching excel_converter.py format
- Removes problematic fields like source/status/endpoint_measurements
- Maintains compatibility with existing evaluation pipeline

TEST FILE: tests/test_unified_extraction_converter.py
"""

import pandas as pd
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from src.utils.logging_utils import get_logger

logger = get_logger(__name__)

class UnifiedExtractionConverter:
    """Unified converter for both annotated and unannotated extraction results."""
    
    def __init__(self, file_path: str, annotated: bool = False, sheet_name: str = 'Endpoints'):
        self.file_path = Path(file_path).resolve()
        self.annotated = annotated  # Flag to indicate if data has human annotations
        self.sheet_name = sheet_name
        self.df: Optional[pd.DataFrame] = None
        self.priority_mapping: Optional[Dict[str, List[str]]] = None
        self.extraction_to_gt_mapping: Optional[Dict[str, str]] = None
    
    def load_data_file(self):
        """Load data file (Excel for annotated, CSV for unannotated)."""
        try:
            if not self.file_path.is_file():
                raise FileNotFoundError(f"No file found at {self.file_path}")
            
            if self.annotated:
                # Load Excel file for annotated data
                self.df = pd.read_excel(self.file_path, sheet_name=self.sheet_name, header=0)
                logger.info(f"Loaded {len(self.df)} annotated records from {self.file_path.name}")
            else:
                # Load CSV file for unannotated data
                self.df = pd.read_csv(self.file_path, encoding='utf-8-sig')
                logger.info(f"Loaded {len(self.df)} unannotated records from {self.file_path.name}")
            
            # Remove any extra whitespace from column names
            self.df.columns = self.df.columns.str.strip()
            
            # Ensure we have an 'id' column for paper grouping
            if 'id' not in self.df.columns:
                # Check if there's an 'Unnamed: 0' column that contains IDs
                if 'Unnamed: 0' in self.df.columns:
                    self.df.rename(columns={'Unnamed: 0': 'id'}, inplace=True)
                else:
                    raise ValueError("Data file must contain an 'id' column for paper identification")
            
            logger.info(f"Available columns: {list(self.df.columns)}")

        except Exception as e:
            logger.error(f"Error loading data file: {e}")
            raise
    
    def load_priority_mapping(self, priority_mapping_path: Optional[str] = None):
        """Load priority mapping from JSON file."""
        try:
            if priority_mapping_path:
                # Use provided path
                priority_json_path = Path(priority_mapping_path)
            else:
                # Look for priority_mapping.json in the same directory as the data file
                priority_json_path = self.file_path.parent / 'priority_mapping.json'
                
                # If not found, try looking in the raw_groundtruth directory
                if not priority_json_path.exists():
                    # Navigate to raw_groundtruth from extraction directory
                    potential_path = self.file_path.parent.parent / 'raw_groundtruth' / 'priority_mapping.json'
                    if potential_path.exists():
                        priority_json_path = potential_path
            
            if priority_json_path.exists():
                with open(priority_json_path, 'r', encoding='utf-8') as f:
                    priority_data = json.load(f)
                    self.priority_mapping = priority_data.get('priority_mapping_ground_truth', priority_data.get('priority_mapping', {}))
                    
                    # Load GT-to-extraction mapping and create reverse mapping
                    gt_to_extraction = priority_data.get('ground_truth_to_extraction_mapping', {})
                    self.extraction_to_gt_mapping = {v: k for k, v in gt_to_extraction.items() if v is not None}
                    
                    logger.info(f"Loaded priority mapping from {priority_json_path} with {len(self.priority_mapping)} categories")
                    logger.info(f"Loaded extraction-to-GT mapping with {len(self.extraction_to_gt_mapping)} mappings")
            else:
                logger.warning(f"Priority mapping file not found at {priority_json_path}")
                self.priority_mapping = {}
                self.extraction_to_gt_mapping = {}
                
        except Exception as e:
            logger.error(f"Error loading priority mapping: {e}")
            self.priority_mapping = {}
            self.extraction_to_gt_mapping = {}
    
    def get_endpoint_priority(self, endpoint_name: str) -> Optional[str]:
        """Get priority for a given extraction endpoint name."""
        if not self.priority_mapping:
            return None
            
        # First, try to map extraction endpoint name to ground truth name
        gt_endpoint_name = None
        if self.extraction_to_gt_mapping and endpoint_name in self.extraction_to_gt_mapping:
            gt_endpoint_name = self.extraction_to_gt_mapping[endpoint_name]
            logger.debug(f"Mapped extraction endpoint '{endpoint_name}' to GT endpoint '{gt_endpoint_name}'")
        else:
            # Fallback: try direct matching with GT endpoint names
            gt_endpoint_name = endpoint_name
            
        # Search through all priority categories using GT endpoint name
        for priority, endpoints in self.priority_mapping.items():
            if gt_endpoint_name in endpoints:
                return priority
                
        return None
    
    def get_unique_paper_ids(self) -> List[str]:
        """Get list of unique paper IDs in the dataset."""
        if self.df is None:
            raise ValueError("Data file not loaded. Call load_data_file() first.")
        
        unique_ids = self.df['id'].unique().tolist()
        logger.info(f"Found {len(unique_ids)} unique paper IDs")
        return unique_ids
    
    def process_paper(self, paper_id: str) -> List[Dict[str, Any]]:
        """Process a single paper and return extraction data in standardized format."""
        if self.df is None:
            raise ValueError("Data file not loaded. Call load_data_file() first.")
            
        # Filter data for this paper
        paper_data = self.df[self.df['id'] == paper_id]
        
        if paper_data.empty:
            logger.warning(f"No data found for paper {paper_id}")
            return []
        
        # For annotated data, filter out records with empty human_classification
        if self.annotated:
            initial_count = len(paper_data)
            paper_data = paper_data.dropna(subset=['human_classification'])
            filtered_count = len(paper_data)
            
            if filtered_count < initial_count:
                skipped = initial_count - filtered_count
                logger.info(f"Filtered out {skipped} records with empty human_classification for paper {paper_id}")
            
            if paper_data.empty:
                logger.warning(f"No human-validated records found for paper {paper_id}")
                return []
        
        results = []
        for idx, row in paper_data.iterrows():
            try:
                # Convert row to standardized JSON format 
                json_record = self.convert_row_to_standard_format(row, idx)
                results.append(json_record)
                
            except Exception as e:
                logger.error(f"Error processing row {idx} for paper {paper_id}: {e}")
                continue
        
        logger.info(f"Processed {len(results)} extraction records for paper {paper_id}")
        return results
    
    def convert_row_to_standard_format(self, row: pd.Series, row_index: int) -> Dict[str, Any]:
        """Convert row to standardized extraction format (excel_converter.py structure)."""
        
        # Standard extraction fields - matching excel_converter.py structure
        extraction_columns = {
            # Core extraction fields
            'id': 'id',
            'adc_name': 'adc_name', 
            'model_name': 'model_name',
            'model_type': 'model_type',
            'experiment_type': 'experiment_type',
            'endpoint_name': 'endpoint_name',
            'measured_value': 'measured_value',
            'measured_dose': 'measured_dose', 
            'measured_concentration': 'measured_concentration',
            'measured_timepoint': 'measured_timepoint',
            'measured_death_percentage': 'measured_death_percentage',
            'endpoint_citations': 'endpoint_citations',
        }
        
        # Human annotation fields (only for annotated data)
        if self.annotated:
            extraction_columns.update({
                'human_classification': 'human_classification',
                'human_reasoning': 'human_reasoning',
                'Comments': 'comments',  # Map Comments to standardized name
            })
        
        # Fields to completely ignore (problematic fields from csv_converter)
        ignored_fields = {
            # Problematic fields that cause structural inconsistency
            'source', 'status', 'status_cat', 'endpoint_measurements',
            'endpoint_units',  # Not used in excel_converter
            # Evaluation result fields (will be re-computed)
            'matched', 'gt_id', 'reason', 'reasoning',
            # Unwanted orphan fields
            'expert_reasoning', 'Priority', 'gt_status',
            # Any unnamed columns or other metadata
            'Unnamed: 0', 'Human validation', 'Regression evaluation'
        }
        
        json_record = {}
        
        # Extract only the desired columns
        for excel_col, standard_col in extraction_columns.items():
            if excel_col in row.index:
                value = row[excel_col]
                
                # Convert pandas NA/NaN to appropriate default value
                if pd.isna(value):
                    # Use empty string for required string fields, None for optional fields
                    if standard_col in ['id', 'adc_name', 'model_name', 'endpoint_name']:
                        json_record[standard_col] = ""
                    else:
                        json_record[standard_col] = None
                else:
                    # Keep all values as strings to preserve original formatting
                    cleaned_value = str(value).strip()
                    # Use empty string for required string fields if empty, None for optional fields
                    if not cleaned_value:
                        if standard_col in ['id', 'adc_name', 'model_name', 'endpoint_name']:
                            json_record[standard_col] = ""
                        else:
                            json_record[standard_col] = None
                    else:
                        json_record[standard_col] = cleaned_value
            else:
                # Column not present, set appropriate default
                if standard_col in ['id', 'adc_name', 'model_name', 'endpoint_name']:
                    json_record[standard_col] = ""
                else:
                    json_record[standard_col] = None
        
        # For unannotated data, explicitly set annotation fields to None
        if not self.annotated:
            json_record['human_classification'] = None
            json_record['human_reasoning'] = None
            json_record['comments'] = None
        
        # Set type field to "endpoint" (required by evaluation system)
        json_record['type'] = 'endpoint'
        
        # Add paper_id field (copy from id for backward compatibility)
        json_record['paper_id'] = json_record.get('id', '')
        
        # Add priority field based on endpoint name
        endpoint_name = json_record.get('endpoint_name')
        priority = self.get_endpoint_priority(endpoint_name) if endpoint_name else None
        json_record['priority'] = priority
        
        # Add row index for tracking
        json_record['_row_index'] = row_index
        
        # Log any ignored fields that were present (for debugging)
        present_ignored = [col for col in ignored_fields if col in row.index and not pd.isna(row[col])]
        if present_ignored:
            logger.debug(f"Ignored problematic fields for paper {json_record['id']}: {present_ignored}")
        
        return json_record
    
    def save_extraction_results(self, output_dir: str, validate_only: bool = False) -> Dict[str, int]:
        """Save extraction results JSON files for all papers."""
        if self.df is None:
            raise ValueError("Data file not loaded. Call load_data_file() first.")
        
        output_path = Path(output_dir).resolve()
        if not validate_only:
            output_path.mkdir(parents=True, exist_ok=True)
        
        paper_ids = self.get_unique_paper_ids()
        stats = {"processed": 0, "skipped": 0, "errors": 0}
        
        for paper_id in paper_ids:
            try:
                # Process paper data
                paper_records = self.process_paper(paper_id)
                
                if not paper_records:
                    logger.warning(f"No extraction records for paper {paper_id}")
                    stats["skipped"] += 1
                    continue
                
                if not validate_only:
                    # Save to file
                    output_file = output_path / f"{paper_id.lower()}_results.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(paper_records, f, indent=2, ensure_ascii=False)
                    
                    logger.info(f"Saved {len(paper_records)} records to {output_file.name}")
                
                stats["processed"] += 1
                
            except Exception as e:
                logger.error(f"Error processing paper {paper_id}: {e}")
                stats["errors"] += 1
                continue
        
        logger.info(f"Processing complete. Processed: {stats['processed']}, "
                   f"Skipped: {stats['skipped']}, Errors: {stats['errors']}")
        
        return stats