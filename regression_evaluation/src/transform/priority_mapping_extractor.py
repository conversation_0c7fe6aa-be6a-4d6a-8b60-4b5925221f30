#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Priority Mapping Extractor for Ground Truth CSV files.

This script analyzes ground truth CSV files to extract the mapping between
Priority values and endpoint names, then saves the mapping as a JSON file
in the same directory as the input CSV.

Usage:
    python -m src.transform.priority_mapping_extractor --csv-path path/to/groundtruth.csv
"""

import argparse
import json
import pandas as pd
import sys
from pathlib import Path
from regression_evaluation.src.utils.logging_utils import get_logger

logger = get_logger(__name__)

def extract_priority_mapping(csv_path):
    """
    Extract priority-to-endpoint mapping from ground truth CSV.
    
    Args:
        csv_path (str): Path to the ground truth CSV file
        
    Returns:
        dict: Dictionary mapping Priority values to lists of endpoint names
    """
    try:
        # Read the CSV file
        df = pd.read_csv(csv_path)
        
        # Check if required columns exist
        if 'Priority' not in df.columns:
            logger.error("'Priority' column not found in CSV file")
            return None
        if 'endpoint_name' not in df.columns:
            logger.error("'endpoint_name' column not found in CSV file")
            return None
            
        # Remove rows with NaN in endpoint_name only (keep NaN in Priority for 'Unspecified' category)
        df_clean = df.dropna(subset=['endpoint_name']).copy()
        
        # Clean Priority values by stripping whitespace to merge 'Prioritize' and 'Prioritize ' 
        # For NaN values in Priority, fill with 'Unspecified'
        df_clean['Priority_cleaned'] = df_clean['Priority'].fillna('Unspecified').str.strip()
        
        # Group by cleaned Priority and get unique endpoint names for each group
        priority_mapping = (df_clean.groupby('Priority_cleaned')['endpoint_name']
                           .apply(lambda x: sorted(x.unique().tolist()))
                           .to_dict())
        
        # Log summary statistics
        logger.info(f"Extracted priority mapping:")
        logger.info(f"- Total priority categories: {len(priority_mapping)}")
        for priority, endpoints in priority_mapping.items():
            logger.info(f"- '{priority}': {len(endpoints)} endpoints")
            
        return priority_mapping
    except Exception as e:
        logger.error(f"Error extracting priority mapping: {e}")
        return None

def create_gt_to_extraction_mapping():
    """
    Create mapping from ground truth endpoint names to extraction endpoint names.
    
    Returns:
        dict: Dictionary mapping GT endpoint names to extraction endpoint names
    """
    # Manual mapping based on the observed patterns
    gt_to_extraction = {
        "ADC AUC": "ADC_AUC",
        "ADC Cmax": "ADC_CMAX", 
        "ADC EC₅₀": "ADC_EC50",
        "ADC GI₅₀": "ADC_GI50",
        "ADC IC₅₀": "ADC_IC50",
        "ADC Internalization": "ADC_INTERNALIZATION",
        "ADC Kd": "ADC_KD",
        "ADC t 1/2": "ADC_T_HALF",
        "Albumin reconjugation": None,  # Not found in extraction
        "Anti-Drug Antibodies": "ANTI_DRUG_ANTIBODIES",
        "Anti-tumor activity Dose": "ANTI_TUMOR_ACTIVITY_DOSE",
        "Antigen Expression (Biomarker High or Low Model)": "ANTIGEN_EXPRESSION",
        "BM neg cell decrease": "BM_NEG_CELL_DECREASE",
        "BM pos cell decrease": "BM_POS_CELL_DECREASE",
        "Cshed Antigen Shedding": None,  # Not found in extraction
        "Decreased Body Weight": "DECREASED_BODY_WEIGHT",
        "Decreased Food Consumption": "DECREASED_FOOD_CONSUMPTION",
        "GI Issues": "GI_ISSUES",
        "HNSTD": "HNSTD",
        "Increased ALT": "INCREASED_ALT",
        "Increased AST": "INCREASED_AST",
        "Lethal Dose": "LETHAL_DOSE",
        "Objective Response Rate (ORR)": "OBJECTIVE_RESPONSE_RATE",
        "Payload  EC₅₀ ": "PAYLOAD_EC50",
        "Payload AUC": "PAYLOAD_AUC",
        "Payload Cmax": "PAYLOAD_CMAX",
        "Payload IC₅₀": "PAYLOAD_IC50",
        "Payload release": "PAYLOAD_RELEASE",
        "Payload t 1/2": "PAYLOAD_T_HALF",
        "Pulmonary Toxicity": "PULMONARY_TOXICITY",
        "Reduced Albumin": "REDUCED_ALBUMIN",
        "Reduced Hemoglobin": "REDUCED_HEMOGLOBIN",
        "Reduced Lymphocytes": "REDUCED_LYMPHOCYTES",
        "Reduced Neutrophils": "REDUCED_NEUTROPHILS",
        "Reduced Red Blood Count": "REDUCED_RED_BLOOD_COUNT",
        "Reticulocyte": "RETICULOCYTE",
        "Specific Antigen Expression (H-Score)": "SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE",
        "Specific Antigen Expression (H-score)": "SPECIFIC_ANTIGEN_EXPRESSION_H_SCORE",  # Same as above
        "TAb AUC": "TAB_AUC",
        "TAb Cmax": "TAB_CMAX",
        "TAb t 1/2": "TAB_T_HALF",
        "Target Organ Issues": "TARGET_ORGAN_ISSUES",
        "Toxicity Dosing Regimen": "TOXICITY_DOSING_REGIMEN",
        "Tumor growth inhibition": "TUMOR_GROWTH_INHIBITION",
        "White Blood Cells": "WHITE_BLOOD_CELLS"
    }
    
    return gt_to_extraction

def save_priority_mapping_json(priority_mapping, csv_path):
    """
    Save priority mapping as JSON file in the same directory as the CSV.
    
    Args:
        priority_mapping (dict): Priority to endpoint mapping
        csv_path (str): Path to the original CSV file
        
    Returns:
        str: Path to the saved JSON file
    """
    try:
        # Get directory of the CSV file
        csv_dir = Path(csv_path).parent
        
        # Create JSON file path
        json_path = csv_dir / 'priority_mapping.json'
        
        # Create GT to extraction mapping
        gt_to_extraction = create_gt_to_extraction_mapping()
        
        # Prepare JSON data with metadata
        json_data = {
            "metadata": {
                "source_csv": str(Path(csv_path).name),
                "description": "Mapping of Priority values to endpoint names and GT-to-extraction endpoint name mapping",
                "total_categories": len(priority_mapping),
                "total_endpoints": sum(len(endpoints) for endpoints in priority_mapping.values()),
                "gt_to_extraction_mappings": len([v for v in gt_to_extraction.values() if v is not None])
            },
            "priority_mapping_ground_truth": priority_mapping,
            "ground_truth_to_extraction_mapping": gt_to_extraction
        }
        
        # Save JSON file
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Priority mapping saved to: {json_path}")
        logger.info(f"Added GT-to-extraction mapping with {len([v for v in gt_to_extraction.values() if v is not None])} mappings")
        return str(json_path)
    except Exception as e:
        logger.error(f"Error saving priority mapping JSON: {e}")
        return None

def main():
    """Main CLI function for priority mapping extraction."""
    parser = argparse.ArgumentParser(
        description="Extract priority-to-endpoint mapping from ground truth CSV",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Extract priority mapping from CSV
  python -m src.transform.priority_mapping_extractor --csv-path data_04_scalup_no_annotations/raw_groundtruth/03_AZ_ADC_ScaleUp_Groundtruth_curation_master(Endpoints)(1).csv
        """
    )
    
    parser.add_argument(
        '--csv-path',
        required=True,
        help='Path to ground truth CSV file'
    )
    
    args = parser.parse_args()
    
    try:
        # Validate input file exists
        csv_path = Path(args.csv_path)
        if not csv_path.exists():
            logger.error(f"CSV file not found: {csv_path}")
            sys.exit(1)
        
        # Validate file type
        if not csv_path.suffix.lower() == '.csv':
            logger.error("Input file must be a CSV file (.csv)")
            sys.exit(1)
        
        logger.info(f"Processing CSV file: {csv_path}")
        
        # Extract priority mapping
        priority_mapping = extract_priority_mapping(str(csv_path))
        
        if not priority_mapping:
            logger.error("Failed to extract priority mapping")
            sys.exit(1)
        
        # Save as JSON
        json_path = save_priority_mapping_json(priority_mapping, str(csv_path))
        
        if not json_path:
            logger.error("Failed to save priority mapping JSON")
            sys.exit(1)
        
        logger.info(f"""
Priority mapping extraction complete!
- Source CSV: {csv_path}
- Output JSON: {json_path}
- Priority categories: {len(priority_mapping)}
- Total endpoints: {sum(len(endpoints) for endpoints in priority_mapping.values())}
        """)
        
    except Exception as e:
        logger.error(f"Priority mapping extraction failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()