"""
Data loading implementations for the ADC evaluation system.

This module provides implementations of the DataLoader interface with
proper error handling, validation, and logging.

TEST FILES: tests/test_main.py, tests/test_refactored_code.py, tests/test_gt_removal.py, tests/test_integration_real_api.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test files
are updated or added to maintain test coverage and validate the changes.
"""

from typing import List, Optional, Dict, Any, Tuple
from .interfaces import DataLoader
from .models import ExtractedEndpoint
from .exceptions import DataLoadingError, PaperNotFoundError
from .utils.file_utils import FileUtils
from .utils.validation_utils import ValidationUtils
from .utils.logging_utils import LoggerMixin, log_async_function_call
from .utils.constants import Constants


class FileDataLoader(DataLoader, LoggerMixin):
    """File-based data loader for ADC evaluation data."""
    
    def __init__(self, data_dir: str = Constants.DEFAULT_DATA_DIR, priority_filter: str = 'all'):
        """Initialize data loader with data directory and priority filter."""
        self.data_dir = data_dir
        self.priority_filter = priority_filter
        self.logger.info(f"Initialized file data loader with data directory: {data_dir}, priority filter: {priority_filter}")
    
    def load_ground_truth(self, paper_id: str) -> Dict[str, Any]:
        """Load ground truth data for a paper."""
        try:
            self.logger.debug(f"Loading ground truth for paper: {paper_id}")
            
            # Check if paper files exist
            files_exist, missing_files = FileUtils.check_paper_files_exist(paper_id, self.data_dir)
            if not files_exist:
                raise PaperNotFoundError(paper_id, missing_files)
            
            # Load ground truth data
            gt_doc = FileUtils.load_ground_truth(paper_id, self.data_dir)
            
            # Apply priority filtering if enabled
            if self.priority_filter == 'prioritized_and_proxied':
                original_endpoints = gt_doc.get('records', gt_doc.get('endpoints', []))
                filtered_endpoints = [
                    endpoint for endpoint in original_endpoints
                    if endpoint.get('priority') in ['Prioritize', 'Proxied']
                ]
                
                # Update the document with filtered endpoints
                if 'records' in gt_doc:
                    gt_doc['records'] = filtered_endpoints
                elif 'endpoints' in gt_doc:
                    gt_doc['endpoints'] = filtered_endpoints
                
                self.logger.info(f"Priority filtered ground truth for {paper_id}: {len(filtered_endpoints)}/{len(original_endpoints)} endpoints (Prioritize/Proxied only)")
            
            # Skip validation for simplified format
            
            record_count = len(gt_doc.get('records', gt_doc.get('endpoints', [])))
            self.logger.info(f"Successfully loaded ground truth for paper {paper_id}: {record_count} records")
            return gt_doc
            
        except Exception as e:
            if isinstance(e, (PaperNotFoundError, DataLoadingError)):
                raise
            raise DataLoadingError(f"Error loading ground truth for paper {paper_id}: {str(e)}")
    
    def load_extraction_results(self, paper_id: str) -> List[Tuple[ExtractedEndpoint, Dict[str, Any]]]:
        """Load and normalize extraction results for a paper."""
        try:
            self.logger.debug(f"Loading extraction results for paper: {paper_id}")
            
            # 1. Load raw data and get tuples of (Pydantic model, raw_dict)
            extraction_tuples = FileUtils.load_extraction_results(paper_id, self.data_dir)

            # 2. Apply priority filtering if enabled
            if self.priority_filter == 'prioritized_and_proxied':
                original_count = len(extraction_tuples)
                filtered_tuples = [
                    (endpoint_model, raw_dict) for endpoint_model, raw_dict in extraction_tuples
                    if raw_dict.get('priority') in ['Prioritize', 'Proxied']
                ]
                extraction_tuples = filtered_tuples
                self.logger.info(f"Priority filtered extraction results for {paper_id}: {len(extraction_tuples)}/{original_count} endpoints (Prioritize/Proxied only)")

            # 3. Validate the raw dictionaries
            raw_rows = [raw_dict for _, raw_dict in extraction_tuples]
            validation_report = ValidationUtils.validate_extraction_results(raw_rows)
            if not validation_report["is_valid"]:
                raise DataLoadingError(
                    f"Extraction results validation failed for paper {paper_id}",
                    {"validation_errors": validation_report["errors"]}
                )
            
            self.logger.info(f"Successfully loaded and validated extraction results for paper {paper_id}: {len(extraction_tuples)} endpoints")
            return extraction_tuples
            
        except Exception as e:
            if isinstance(e, (PaperNotFoundError, DataLoadingError)):
                raise
            raise DataLoadingError(f"Error loading extraction results for paper {paper_id}: {str(e)}")
    
    def load_paper_data(self, paper_id: str) -> tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Load both ground truth and extraction results for a paper."""
        try:
            self.logger.info(f"Loading complete paper data for: {paper_id}")
            
            # Load both datasets
            gt_doc = self.load_ground_truth(paper_id)
            extraction_rows = self.load_extraction_results(paper_id)
            
            # Skip paper ID consistency validation for simplified format
            
            self.logger.info(f"Successfully loaded complete paper data for {paper_id}")
            return gt_doc, extraction_rows
            
        except Exception as e:
            if isinstance(e, (PaperNotFoundError, DataLoadingError)):
                raise
            raise DataLoadingError(f"Error loading paper data for {paper_id}: {str(e)}")


class MockDataLoader(DataLoader, LoggerMixin):
    """Mock data loader for testing purposes."""
    
    def __init__(self, mock_data: Optional[dict] = None):
        """Initialize mock data loader with optional mock data."""
        self.mock_data = mock_data or {}
        self.logger.info("Initialized mock data loader")
    
    def load_ground_truth(self, paper_id: str) -> Dict[str, Any]:
        """Load mock ground truth data."""
        self.logger.debug(f"Loading mock ground truth for paper: {paper_id}")
        
        if paper_id in self.mock_data.get("ground_truth", {}):
            return self.mock_data["ground_truth"][paper_id]
        
        # Default mock data
        return Dict[str, Any](
            paper_id=paper_id,
            endpoints=[
                GTEndpoint(
                    paper_id=paper_id,
                    adc_name="Mock ADC",
                    model_name="Mock Model",
                    endpoint_name="Mock Endpoint",
                    endpoint_value=1.0,
                    endpoint_units="mock_unit"
                )
            ]
        )
    
    def load_extraction_results(self, paper_id: str) -> List[Dict[str, Any]]:
        """Load mock extraction results."""
        self.logger.debug(f"Loading mock extraction results for paper: {paper_id}")
        
        if paper_id in self.mock_data.get("extraction_results", {}):
            return self.mock_data["extraction_results"][paper_id]
        
        # Default mock data
        return [
            ExtractedEndpoint(
                paper_id=paper_id,
                adc_name="Mock ADC",
                model_name="Mock Model",
                endpoint_name="Mock Endpoint",
                endpoint_measurements=[
                    Measurement(
                        measurement_id="mock_1",
                        value=ExtractedValueObject(
                            value=1.0,
                            unit="mock_unit",
                            citation="Mock citation"
                        )
                    )
                ]
            )
        ]
    
    def load_paper_data(self, paper_id: str) -> tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """Load both ground truth and extraction results for a paper."""
        self.logger.debug(f"Loading complete mock paper data for: {paper_id}")
        
        gt_doc = self.load_ground_truth(paper_id)
        extraction_rows = self.load_extraction_results(paper_id)
        
        return gt_doc, extraction_rows