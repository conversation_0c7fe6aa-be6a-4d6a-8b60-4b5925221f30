"""
Configuration module for the ADC evaluation system.

This module provides centralized configuration management with support for
environment variables and multiple OpenAI providers.

TEST FILE: tests/test_config.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test file
are updated or added to maintain test coverage and validate the changes.
"""

import os
from openai import OpenAI, AzureOpenAI
from dotenv import load_dotenv
from .utils.constants import Constants

load_dotenv()


class Config:
    """Configuration class for the ADC evaluation system."""
    
    # OpenAI credentials
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Azure OpenAI credentials
    AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_KEY = os.getenv("AZURE_OPENAI_KEY") 
    AZURE_OPENAI_VERSION = os.getenv("AZURE_OPENAI_VERSION", "2024-12-01-preview")
    
    # Model configuration - get from constants
    PRIMARY_MODEL = Constants.PRIMARY_MODEL
    FALLBACK_MODEL = Constants.FALLBACK_MODEL
    
    # API configuration - get from constants
    MAX_RETRIES = Constants.MAX_RETRIES
    TIMEOUT_SECONDS = Constants.TIMEOUT_SECONDS
    
    # Additional configuration
    CONFIDENCE_THRESHOLD = float(os.getenv("CONFIDENCE_THRESHOLD", str(Constants.DEFAULT_CONFIDENCE_THRESHOLD)))
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """Get a summary of current configuration."""
        return {
            "has_openai_key": bool(cls.OPENAI_API_KEY),
            "has_azure_credentials": bool(cls.AZURE_OPENAI_ENDPOINT and cls.AZURE_OPENAI_KEY),
            "primary_model": cls.PRIMARY_MODEL,
            "fallback_model": cls.FALLBACK_MODEL,
            "max_retries": cls.MAX_RETRIES,
            "timeout_seconds": cls.TIMEOUT_SECONDS,
            "confidence_threshold": cls.CONFIDENCE_THRESHOLD
        }
    
def get_openai_client():
    """Get OpenAI client - automatically detects Azure vs regular OpenAI based on credentials"""
    # Check if Azure credentials are provided
    if Config.AZURE_OPENAI_ENDPOINT and Config.AZURE_OPENAI_KEY:
        return AzureOpenAI(
            azure_endpoint=Config.AZURE_OPENAI_ENDPOINT,
            api_key=Config.AZURE_OPENAI_KEY,
            api_version=Config.AZURE_OPENAI_VERSION
        )
    # Fall back to regular OpenAI
    elif Config.OPENAI_API_KEY:
        return OpenAI(
            api_key=Config.OPENAI_API_KEY
        )
    else:
        raise ValueError(
            "No OpenAI credentials found. Please provide either:\n"
            "- OPENAI_API_KEY for regular OpenAI, or\n"
            "- AZURE_OPENAI_ENDPOINT and AZURE_OPENAI_KEY for Azure OpenAI"
        )

