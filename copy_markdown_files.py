#!/usr/bin/env python3
import os
import shutil
from pathlib import Path
import re

def find_and_copy_markdown_files():
    """Find and copy markdown files matching target paper IDs"""
    
    # Target paper IDs to find
    target_ids = [
        'W4390743855', 'W2314772071', 'W4226170358', 'W4376958886',
        'W4226097966', 'W3093537091', 'W4280631979', 'W4284897854',
        'W4400124600', 'W4387230410', 'W2911746982'
    ]
    
    # Source directory
    source_dir = Path('output_4.1')
    
    # Destination directory
    dest_dir = Path('markdowns_of_interest')
    
    # Create destination directory
    dest_dir.mkdir(exist_ok=True)
    print(f"Created directory: {dest_dir}")
    
    # Track found and missing files
    found_files = []
    missing_files = []
    
    # Get all markdown files in source directory
    if source_dir.exists():
        md_files = list(source_dir.glob("*.md"))
        print(f"Found {len(md_files)} markdown files in {source_dir}")
    else:
        print(f"Source directory {source_dir} does not exist!")
        return
    
    # Search for each target ID
    for target_id in target_ids:
        print(f"\nSearching for: {target_id}")
        
        # Create patterns for both uppercase and lowercase versions
        patterns = [
            target_id.lower(),  # w4390743855
            target_id.upper(),  # W4390743855
        ]
        
        found = False
        for md_file in md_files:
            filename = md_file.name
            
            # Check if any pattern matches the filename
            for pattern in patterns:
                if pattern in filename:
                    # Copy the file
                    dest_file = dest_dir / filename
                    shutil.copy2(md_file, dest_file)
                    found_files.append((target_id, filename))
                    print(f"  ✓ Found and copied: {filename}")
                    found = True
                    break
            
            if found:
                break
        
        if not found:
            missing_files.append(target_id)
            print(f"  ✗ Not found: {target_id}")
    
    # Summary
    print(f"\n{'='*50}")
    print(f"SEARCH AND COPY SUMMARY")
    print(f"{'='*50}")
    print(f"Total target IDs: {len(target_ids)}")
    print(f"Files found and copied: {len(found_files)}")
    print(f"Files missing: {len(missing_files)}")
    
    if found_files:
        print(f"\n✅ Successfully copied files:")
        for target_id, filename in found_files:
            print(f"  - {target_id} → {filename}")
    
    if missing_files:
        print(f"\n❌ Missing files (not found in source directory):")
        for target_id in missing_files:
            print(f"  - {target_id}")
    
    # Verify the destination directory
    print(f"\n{'='*50}")
    print(f"VERIFICATION")
    print(f"{'='*50}")
    copied_files = list(dest_dir.glob("*.md"))
    print(f"Files in {dest_dir} ({len(copied_files)} total):")
    for file in sorted(copied_files):
        print(f"  - {file.name}")
    
    # Success rate
    success_rate = (len(found_files) / len(target_ids)) * 100
    print(f"\n📊 Success Rate: {success_rate:.1f}% ({len(found_files)}/{len(target_ids)})")

if __name__ == "__main__":
    find_and_copy_markdown_files()
