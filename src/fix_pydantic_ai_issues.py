"""
Fix pydantic-ai compatibility issues
"""
import re

# Read the original file
with open('extraction_pipeline_parallel_strurcture.py', 'r') as f:
    content = f.read()

print("🔧 Fixing pydantic-ai compatibility issues...")

# Fix 1: Update OpenAIModel to OpenAIChatModel
content = re.sub(
    r'from pydantic_ai\.models\.openai import OpenAIModel',
    'from pydantic_ai.models.openai import OpenAIChatModel',
    content
)

content = re.sub(
    r'OpenAIModel\(',
    'OpenAIChatModel(',
    content
)

# Fix 2: Remove result_type and result_retries from Agent initialization
# This is more complex, so let's do a targeted fix
lines = content.split('\n')
fixed_lines = []
in_agent_init = False
agent_indent = 0

for line in lines:
    if 'Agent(' in line and ('adc_agent' in line or 'model_agent' in line or 'endpoint_agent' in line):
        in_agent_init = True
        agent_indent = len(line) - len(line.lstrip())
        fixed_lines.append(line)
    elif in_agent_init:
        current_indent = len(line) - len(line.lstrip())
        
        # Check if we're still in the Agent initialization
        if line.strip() == '' or current_indent > agent_indent or line.strip().startswith('#'):
            # Skip result_type and result_retries lines
            if 'result_type=' not in line and 'result_retries=' not in line:
                fixed_lines.append(line)
        elif line.strip() == ')' or (current_indent <= agent_indent and line.strip() != ''):
            # End of Agent initialization
            in_agent_init = False
            fixed_lines.append(line)
        else:
            # Skip result_type and result_retries lines
            if 'result_type=' not in line and 'result_retries=' not in line:
                fixed_lines.append(line)
    else:
        fixed_lines.append(line)

content = '\n'.join(fixed_lines)

# Fix 3: Update model name (gpt-4.1 doesn't exist)
content = re.sub(r"model_name='gpt-4\.1'", "model_name='gpt-4'", content)

# Write fixed version
with open('extraction_pipeline_parallel_strurcture_fixed.py', 'w') as f:
    f.write(content)

print("✅ Fixed version created: extraction_pipeline_parallel_strurcture_fixed.py")
print("\n🔧 Additional manual fixes needed:")
print("1. Move result_type to the run() method calls")
print("2. Handle retries in the run() method")
print("3. Update any other deprecated pydantic-ai usage")
