import argparse
import pandas as pd
import asyncio
import os
import sqlite3
import logging
import json
from typing import List, Dict, Optional, Literal
from openai import AsyncAzureOpenAI
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from tqdm import tqdm
from dotenv import load_dotenv
from datetime import datetime
from pathlib import Path
from jinja2 import Template
from utils.extraction_pydantic_models import (
    AntibodyDrugConjugate, 
    Model, 
    Endpoint, 
    ExtractionState, 
    ExperimentResult, 
    ModelType, 
    CitedValue,
    CitedEnumValue
)
from langgraph.graph import StateGraph
from pydantic import BaseModel, Field
from trustcall import create_extractor
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI
import re


load_dotenv()

class ListofAntibodyDrugConjugate(BaseModel):
    adcs: List[AntibodyDrugConjugate]
    
class ListofEndpoints(BaseModel):
    endpoints: List[Endpoint]

def calculate_entity_indices(full_text: str, text_snippet: str, entity: str) -> tuple[int, int]:
        """Calculate the start and end indices of an entity in the full text."""
        try:
            logger = logging.getLogger(__name__)

            def normalize_text(text: str) -> str:
                """Normalize text by:
                1. Converting newlines to spaces
                2. Removing trailing periods
                3. Normalizing spaces
                """
                # Convert newlines to spaces first
                text = re.sub(r'\n+', ' ', text)
                
                # Remove trailing period
                text = re.sub(r'\.$', '', text)
                
                # Normalize multiple spaces to single space
                text = re.sub(r'\s+', ' ', text)
                
                return text.strip()

            # Create a mapping of normalized positions to original positions
            original_positions = []
            
            for i, _ in enumerate(full_text):
                
                original_positions.append(i)

            # Normalize all texts
            normalized_snippet = normalize_text(text_snippet)
            normalized_entity = normalize_text(entity)
            normalized_text = normalize_text(full_text)
            
            logger.debug(f"Normalized snippet: '{normalized_snippet}'")
            logger.debug(f"Normalized text excerpt: '{normalized_text[:100]}'")
            
            # Find the snippet in normalized text
            snippet_start = normalized_text.lower().find(normalized_snippet.lower())
            if snippet_start == -1:
                return 0, 0
                
            # Find the entity within the snippet
            entity_in_snippet = normalized_snippet.lower().find(normalized_entity.lower())
            if entity_in_snippet == -1:
                return 0, 0
            
            # Calculate the actual positions using the mapping
            entity_start_normalized = snippet_start + entity_in_snippet
            entity_end_normalized = entity_start_normalized + len(normalized_entity)
            
            # Map back to original positions
            entity_start = original_positions[entity_start_normalized]
            entity_end = original_positions[entity_end_normalized - 1] + 1
            
            return entity_start, entity_end
        
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Error calculating indices: {str(e)}")
            return 0, 0
# Initialize Azure OpenAI client
client = AsyncAzureOpenAI(
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
    azure_deployment='gpt-4o',
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
)

# Initialize the OpenAI model with Azure provider
llm = AzureChatOpenAI(
    deployment_name="gpt-4o",
    model_name="gpt-4o",
    api_version = os.getenv("AZURE_OPENAI_API_VERSION"),
    temperature=0.0,
    top_p=1.0,
    max_tokens=None,
)
# Initialize agents for each extraction step
adc_agent = create_extractor(
    llm,
    tools=[ListofAntibodyDrugConjugate, calculate_entity_indices]
)
adc_system_prompt = open("prompts/adc_extraction_system_prompt.md").read()

model_agent = create_extractor(
    llm,
    tools=[Model, calculate_entity_indices]
)
model_system_prompt = open("prompts/model_extraction_system_prompt.md").read()

endpoint_agent = create_extractor(
    llm,
    tools=[ListofEndpoints, calculate_entity_indices]
)
endpoint_system_prompt = open("prompts/endpoint_extraction_system_prompt.md").read()

def extract_adcs(state: ExtractionState) -> ExtractionState:
    """Extract ADCs from the text"""
    
    user_prompt = Template(open("prompts/adc_extraction_user_prompt.md").read()).render(
        TEXT=state.raw_text
    )
    result = adc_agent.invoke({
        "messages": [
            SystemMessage(content=adc_system_prompt),
            HumanMessage(content=user_prompt)
        ]
    })
    # Ensure adcs is always a list
    adcs = result["responses"][0].adcs
    
    # Create a default citation
    default_citation = Citation(
        text_snippet="Placeholder",
        start_index=0,
        end_index=0,
        confidence="Low"
    )
    
    state.experiment_results = [
                ExperimentResult(
                    adc=adc,
                    model=Model(
                        model_description=CitedValue[str](
                            citation="",
                            value="",
                            start_ind=0,
                            end_ind=0,
                            confidence="Low",
                        ),
                        model_type=CitedEnumValue[ModelType](
                            citation="",
                            value=ModelType.OTHER,
                            start_ind=0,
                            end_ind=0,
                            confidence="Low",
                            other_specification="",
                        ),
                        cancer_type=CitedValue[str](
                            citation="",
                            value="",
                            start_ind=0,
                            end_ind=0,
                            confidence="Low",
                        ),
                        cancer_subtype=None,
                    ),
                    endpoints=[]
                )
                for adc in adcs
            ]
    
    return state

def extract_models(state: ExtractionState) -> ExtractionState:
    """Extract models for each ADC"""
    for exp_result in state.experiment_results:
        user_prompt = Template(open("prompts/model_extraction_user_prompt.md").read()).render(
            TEXT=state.raw_text,
            ADC=exp_result.adc.model_dump_json()
        )
        result = model_agent.invoke({
            "messages": [
                SystemMessage(content=model_system_prompt),
                HumanMessage(content=user_prompt)
            ]
        })
        # Get first model for now - could be extended to handle multiple models per ADC
        model = result["responses"][0]
        exp_result.model = model  # Update only the model field
    
    return state

def extract_endpoints(state: ExtractionState) -> ExtractionState:
    """Extract endpoints for each ADC and its model"""
    for exp_result in state.experiment_results:
        user_prompt = Template(open("prompts/endpoint_extraction_user_prompt.md").read()).render(
            TEXT=state.raw_text,
            ADC=exp_result.adc.model_dump_json(),
            MODEL=exp_result.model.model_dump_json()
        )
        result = endpoint_agent.invoke({
            "messages": [
                SystemMessage(content=endpoint_system_prompt),
                HumanMessage(content=user_prompt)
            ]
        })
        # Ensure endpoints is a list
        endpoints = result["responses"][0].endpoints
        exp_result.endpoints = endpoints  # Update only the endpoints field
    
    return state

def build_pipeline(adcs_only: bool = False):
    """Build the extraction pipeline
    
    Args:
        adcs_only: If True, only extract ADCs without models and endpoints
    """
    graph = StateGraph(ExtractionState)
    
    # Add ADCs node
    graph.add_node("extract_adcs", extract_adcs)
    
    if not adcs_only:
        # Add additional nodes for full extraction
        graph.add_node("extract_models", extract_models)
        graph.add_node("extract_endpoints", extract_endpoints)
        
        # Add edges for full pipeline
        graph.add_edge("extract_adcs", "extract_models")
        graph.add_edge("extract_models", "extract_endpoints")
    
    # Set entry point
    graph.set_entry_point("extract_adcs")
    
    return graph.compile()

def save_results(state: dict, output_path: Path, adcs_only: bool = False):
    """Save extraction results to JSON file"""
    results = {
        'experiment_results': [
            {
                'adc': exp_result.adc.model_dump(),
                'model': exp_result.model.model_dump() if exp_result.model and not adcs_only else None,
                'endpoints': [endpoint.model_dump() for endpoint in exp_result.endpoints] if not adcs_only and exp_result.endpoints else []
            }
            for exp_result in state['experiment_results']
        ]
    }
    
    # Save to JSON file
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

def process_directory(input_dir: str, output_dir: str, adcs_only: bool = False):
    """Process all markdown files in a directory"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Create output directory if it doesn't exist
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Get all markdown files
    markdown_files = list(input_path.glob("*.md"))
    print(f"Found {len(markdown_files)} markdown files to process")
    
    # Process each file
    for md_file in tqdm(markdown_files, desc="Processing files"):
        try:
            print(f"\nProcessing {md_file.name}")
            
            # Read the markdown file
            with open(md_file, 'r', encoding='utf-8') as f:
                text = f.read()
            
            # Initialize state using Pydantic model
            initial_state = ExtractionState(raw_text=text)
            
            # Build and run pipeline
            graph = build_pipeline(adcs_only=adcs_only)
            final_state = graph.invoke(initial_state)
            
            # Create output JSON path with same name as input file
            json_path = output_path / f"{md_file.stem}.json"
            
            # Save results
            save_results(final_state, json_path, adcs_only=adcs_only)
            print(f"Results saved to {json_path}")
            
        except Exception as e:
            print(f"Error processing {md_file.name}: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extract ADC information from markdown files")
    parser.add_argument("--input-dir", type=str, required=True,
                       help="Directory containing markdown files")
    parser.add_argument("--output-dir", type=str, required=True,
                       help="Directory for output JSON files")
    parser.add_argument("--adcs-only", action="store_true",
                       help="Extract only ADCs without models and endpoints")
    
    args = parser.parse_args()
    
    # Process the directory
    process_directory(args.input_dir, args.output_dir, args.adcs_only)
