import json
import os
import re
from bs4 import BeautifulSoup
from pathlib import Path
from rapidfuzz import process, fuzz

def extract_visible_text_from_html_file(html_file_path):
    with open(html_file_path, "r", encoding="utf-8") as f:
        html_content = f.read()
    soup = BeautifulSoup(html_content, "html.parser")
    for script_or_style in soup(["script", "style"]):
        script_or_style.decompose()
    for br in soup.find_all("br"):
        br.replace_with("\n")
    text = soup.get_text(separator="\n")
    lines = [line.strip() for line in text.splitlines()]
    visible_text = "\n".join(line for line in lines if line)
    return visible_text

def clean_snippet(snippet):
    """Clean the snippet by removing quotes, spaces, and special characters from the beginning and end."""
    if not snippet or not isinstance(snippet, str):
        return ""
    
    # Remove special characters, spaces, and quotes from both beginning and end
    # This pattern matches:
    # ^ - start of string
    # [ ] - character class containing:
    #   \W - any non-word character (equivalent to [^a-zA-Z0-9_])
    #   \s - any whitespace character
    #   "' - quotes (both double and single)
    # + - one or more of the preceding pattern
    # Same pattern with $ for end of string
    snippet = re.sub(r'^[\W\s"\']+|[\W\s"\']+$', '', snippet)
    
    return snippet

def extract_citations(obj, path=()):
    """Extract citations from the object."""
    citations = []
    if isinstance(obj, dict):
        for k, v in obj.items():
            if k == "citation" and isinstance(v, str):
                verbatim = obj.get("verbatim_value", "")
                standard = obj.get("standard_value", "")
                citations.append((path, v, verbatim, standard))
            else:
                citations.extend(extract_citations(v, path + (k,)))
    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            citations.extend(extract_citations(item, path + (i,)))
    return citations

def extract_all_values(obj, path=()):
    """Extract all standard values from the object, regardless of citation."""
    values = []
    if isinstance(obj, dict):
        # Check if this is a value object with standard_value
        if "standard_value" in obj:
            citation = obj.get("citation", "")
            values.append((path, citation, obj.get("standard_value")))
        
        # Continue recursion for all keys
        for k, v in obj.items():
            values.extend(extract_all_values(v, path + (k,)))
    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            values.extend(extract_all_values(item, path + (i,)))
    return values

def path_to_taxonomy(path):
    mapping = {
        "adc_name": ["ADC", "adc_name"],
        "antibody_name": ["ADC", "antibody_name"],
        "clonality": ["ADC", "clonality"],
        "species": ["ADC", "species"],
        "isotype": ["ADC", "isotype"],
        "payload_name": ["ADC", "payload_name"],
        "payload_target": ["ADC", "payload_target"],
        "linker_name": ["ADC", "linker_name"],
        "linker_type": ["ADC", "linker_type"],
        "antigen_name": ["ADC", "antigen_name"],
        "model_description": ["Model", "model_description"],
        "model_type": ["Model", "model_type"],
        "cancer_type": ["Model", "cancer_type"],
        "cancer_subtype": ["Model", "cancer_subtype"],
        "endpoint_type": ["Endpoints", "endpoint_type"],
        "endpoint_name": ["Endpoints", "endpoint_name"],
        "endpoint_quantitative_value": ["Endpoints", "endpoint_quantitative_value"],
        "endpoint_qualitative_value": ["Endpoints", "endpoint_qualitative_value"],
        "endpoint_timepoint": ["Endpoints", "endpoint_timepoint"],
        "endpoint_concentration": ["Endpoints", "endpoint_concentration"]
    }
    for key in mapping:
        if key in path:
            return mapping[key], key
    return [], None

def get_all_attribute_names():
    """Get a list of all attribute names we want to include in the data."""
    return [
        # ADC attributes
        "adc_name", "antibody_name", "clonality", "species", "isotype",
        "payload_name", "payload_target", "linker_name", "linker_type", "antigen_name",
        # Model attributes
        "model_description", "model_type", "cancer_type", "cancer_subtype",
        # Endpoint attributes
        "endpoint_type", "endpoint_name", "endpoint_quantitative_value",
        "endpoint_qualitative_value", "endpoint_timepoint", "endpoint_concentration"
    ]

    

def find_offsets(text, snippet, threshold=70):
    # Extract best match in the entire text using fuzz.partial_ratio (for substring matching)
    match, score, start_idx = process.extractOne(snippet, [text], scorer=fuzz.partial_ratio)

    # If the score is above the threshold, return the match
    if score >= threshold:
        end_idx = start_idx + len(match)
        return start_idx, end_idx
    else:
        return -1, -1





def find_offsets(text, snippet):
    """Find the start and end offsets of a snippet in the text."""
    if not snippet or not isinstance(snippet, str):
        return -1, -1
    
    # Special case for "Not specified" citations
    if snippet.lower().strip() == "not specified in the text" or snippet.lower().strip() == "not specified":
        return -1, -1
    
    # Clean the snippet before searching
    cleaned_snippet = clean_snippet(snippet)
    if not cleaned_snippet:
        return -1, -1
    if cleaned_snippet.lower().strip() == "not specified in the text" or cleaned_snippet.lower().strip() == "not specified":
        return -1, -1
    
    # Try to find the exact snippet first
    start = text.find(snippet)
    if start != -1:
        return start, start + len(snippet)
    
    # If exact match fails, try with the cleaned snippet
    start = text.find(cleaned_snippet)
    if start != -1:
        return start, start + len(cleaned_snippet)
    
    # If still no match, try case-insensitive search
    start = text.lower().find(cleaned_snippet.lower())
    if start != -1:
        return start, start + len(cleaned_snippet)
    
    # If still no match, try fuzzy matching by splitting into words
    words = cleaned_snippet.lower().split()
    if len(words) > 3:  # Only try this for longer snippets
        # Create a sliding window of text to search for most of the words
        text_lower = text.lower()
        best_match_start = -1
        best_match_end = -1
        best_match_score = 0
        
        # Try to find a section of text that contains most of the words
        for i in range(len(text_lower)):
            # Look for a window of text that might contain our snippet
            window_end = min(i + len(cleaned_snippet) * 2, len(text_lower))
            window = text_lower[i:window_end]
            
            # Count how many words from our snippet appear in this window
            score = sum(1 for word in words if word in window)
            
            # If we found a better match, update our best match
            if score > best_match_score and score >= len(words) * 0.7:  # At least 70% of words must match
                # Find the actual boundaries of the match
                first_word_pos = window.find(words[0])
                if first_word_pos != -1:
                    match_start = i + first_word_pos
                    
                    # Try to find the last word
                    last_word_pos = window.rfind(words[-1])
                    if last_word_pos != -1:
                        match_end = i + last_word_pos + len(words[-1])
                        
                        best_match_start = match_start
                        best_match_end = match_end
                        best_match_score = score
        
        if best_match_start != -1:
            return best_match_start, best_match_end
    
    return -1, -1

def create_prenotated_json_for_directory(html_dir, results_dir, output_dir):
    """Process all JSON files in a directory and create pre-annotated JSON files for each ADC, model, endpoint triplet."""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all JSON files in the results directory
    json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    
    # Statistics tracking
    total_citations = 0
    not_found_citations = 0
    not_specified_citations = 0
    
    for json_file in json_files:
        file_id = json_file.split('_results.json')[0]
        html_file = os.path.join(html_dir, f"{file_id}.html")
        
        # Skip if HTML file doesn't exist
        if not os.path.exists(html_file):
            print(f"Warning: HTML file not found for {file_id}, skipping...")
            continue
        
        # Extract visible text from HTML
        visible_text = extract_visible_text_from_html_file(html_file)
        
        # Load JSON results
        with open(os.path.join(results_dir, json_file), "r", encoding="utf-8") as f:
            results = json.load(f)
        
        # Process each experiment result (ADC)
        for i, exp_result in enumerate(results.get('experiment_results', [])):
            adc = exp_result.get('adc', {})
            adc_name = adc.get('adc_name', {}).get('standard_value', f"ADC_{i}")
            
            # Process each model with endpoints
            for j, model_with_endpoints in enumerate(exp_result.get('models_with_endpoints', [])):
                model = model_with_endpoints.get('model', {})
                model_type = model.get('model_type', {}).get('standard_value', f"Model_{j}")
                
                # Process each endpoint
                for k, endpoint in enumerate(model_with_endpoints.get('endpoints', [])):
                    endpoint_type = endpoint.get('endpoint_type', {}).get('standard_value', f"Endpoint_{k}")
                    
                    # Create a unique output file for this ADC-model-endpoint triplet
                    output_file = os.path.join(output_dir, f"{file_id}_{i}_{j}_{k}.json")
                    
                    # Create a subset of the results for this triplet
                    subset_results = {
                        'experiment_results': [{
                            'adc': adc,
                            'models_with_endpoints': [{
                                'model': model,
                                'endpoints': [endpoint]
                            }]
                        }]
                    }
                    
                    # Extract citations for taxonomy annotations
                    citations = extract_citations(subset_results)
                    
                    # Extract all values for textboxes
                    all_values = extract_all_values(subset_results)
                    
                    # Create pre-annotations
                    preannotations = []
                    
                    # Data object to hold textbox values - initialize with all attributes set to empty string
                    data_obj = {
                        "html_url": f"/data/local-files/?d=adc_html/{file_id}.html"
                    }
                    
                    # Initialize all attributes with empty strings
                    for attr_name in get_all_attribute_names():
                        data_obj[attr_name] = ""
                    
                    # Process citations for taxonomy annotations
                    for path, citation, verbatim, standard in citations:
                        # Skip empty citations
                        if not citation or not citation.strip():
                            continue
                        
                        # Check if this is a "Not specified" citation
                        if citation.lower().strip() == "not specified in the text" or citation.lower().strip() == "not specified":
                            not_specified_citations += 1
                            continue
                            
                        total_citations += 1
                        
                        snippet_start, snippet_end = find_offsets(visible_text, citation)
                        if snippet_start == -1:
                            not_found_citations += 1
                            print(f"Warning: Citation not found in text: {citation[:30]}...")
                            continue
                        
                        taxonomy, attr_name = path_to_taxonomy(path)
                        if not taxonomy or not attr_name:
                            print(f"Warning: Taxonomy mapping not found for path: {path}")
                            continue
                        
                        # Modified taxonomy list to include both category and attribute
                        # Format: [["ADC", "adc_name"]] instead of [["adc_name"]]
                        taxonomy_list = [
                            taxonomy
                        ]
                        
                        # Taxonomy annotation (region)
                        preannotations.append({
                            "id": f"region_{attr_name}_{len(preannotations)}",
                            "value": {
                                "start": snippet_start,
                                "end": snippet_end,
                                "text": citation,
                                "taxonomy": taxonomy_list
                            },
                            "from_name": "label",
                            "to_name": "text",
                            "type": "taxonomy",
                            "origin": "prediction"
                        })
                    
                    # Process all values for textboxes
                    for path, citation, standard_value in all_values:
                        taxonomy, attr_name = path_to_taxonomy(path)
                        if not taxonomy or not attr_name:
                            continue
                        
                        # Add standard value to data object if it exists and is not "none"
                        if standard_value and str(standard_value).strip().lower() != "none":
                            data_obj[attr_name] = str(standard_value)
                    
                    # Create the final output
                    output = {
                        "data": data_obj,
                        "predictions": [{
                            "model_version": "pre-annotation-v1",
                            "result": preannotations
                        }]
                    }
                    
                    # Save to file
                    with open(output_file, "w", encoding="utf-8") as f:
                        json.dump(output, f, indent=2)
                    
                    print(f"Pre-annotated JSON saved to: {output_file}")
            break
    # Print citation statistics
    print("\n--- Citation Statistics ---")
    print(f"Total citations processed: {total_citations}")
    print(f"'Not specified' citations: {not_specified_citations}")
    print(f"Citations not found in text: {not_found_citations} ({(not_found_citations/total_citations)*100:.2f}% if total_citations > 0 else 0.00%)")

# --- Example usage ---
if __name__ == "__main__":
    # Replace these paths with your actual directories
    html_dir = "../adc_html"
    results_dir = "../adc_results_multiple_models"
    output_dir = "../labelstudio_preannotations"
    
    create_prenotated_json_for_directory(html_dir, results_dir, output_dir)
