from pydantic import BaseModel, Field
from typing import List, Optional, Literal

class IsADCFocused(BaseModel):
    """Defines the assessment of whether the publication is focused on Antibody-Drug Conjugates (ADCs)."""
    reasoning: str = Field(
        ...,
        description="Detailed explanation for the assessment of whether the publication is focused on Antibody-Drug Conjugates (ADCs)."
    )
    confidence: Literal["High", "Medium", "Low"] = Field(
        ...,
        description="Confidence level in the final assessment of whether the publication focuses on ADCs, based on the reasoning provided."
    )
    final_assessment: bool = Field(
        ...,
        description="Boolean value indicating the final assessment of whether the publication focuses on ADCs. True indicates the publication does focus on ADCs, False indicates it does not."
    )
    adc_names: Optional[List[str]] = Field(
        default_factory=list,
        description="If the publication is ADC-focused, this should be a list of specific names of Antibody-Drug Conjugates mentioned in the title or abstract. If the publication is not ADC-focused, this should be an empty list."
    )

class DiscussesPreclinicalExperiments(BaseModel):
    """Defines the assessment of whether the publication discusses pre-clinical experiments related to ADCs in oncology."""
    reasoning: str = Field(
        ...,
        description="Detailed explanation for the assessment of whether the publication discusses pre-clinical experiments related to ADCs in oncology."
    )
    confidence: Literal["High", "Medium", "Low"] = Field(
        ...,
        description="Confidence level in the final assessment of whether the publication discusses pre-clinical experiments related to ADCs in oncology, based on the reasoning provided."
    )
    final_assessment: bool = Field(
        ...,
        description="Boolean value indicating the final assessment of whether the publication discusses pre-clinical experiments related to ADCs in oncology. True indicates the publication does discuss pre-clinical experiments, False indicates it does not."
    )

class IdentifiedOncologyIndication(BaseModel):
    """Defines an identified oncology disease indication in pre-clinical experiments of ADCs for the publication."""
    reasoning: str = Field(
        ...,
        description="Detailed explanation for identifying this oncology disease indication, citing specific evidence from the abstract."
    )
    confidence: Literal["High", "Medium", "Low"] = Field(
        ...,
        description="Confidence level in identifying this specific oncology indication, based on the reasoning provided."
    )
    cancer_type: str = Field(
        ...,
        description="The broad classification of cancer type being studied in pre-clinical experiments of ADCs (e.g. lung, breast, etc.)."
    )
    cancer_subtype: Optional[str] = Field(
        None,
        description="Specific subtype of cancer being studied in pre-clinical experiments of ADCs (e.g., NSCLC, SCLC, etc. for lung cancer; Triple-negative, HER2+, etc. for breast cancer)"
    )

class IdentifiedPreclinicalStudyFocus(BaseModel):
    """Defines the primary focus of ADC pre-clinical research in the publication."""
    reasoning: str = Field(
        ...,
        description="Detailed explanation for the primary focus of ADC pre-clinical research in the publication."
    )
    confidence: Literal["High", "Medium", "Low"] = Field(
        ...,
        description="Confidence level in identifying and categorizing this specific preclinical study goal of ADC pre-clinical experiments in oncology, based on the reasoning provided."
    )
    primary_focus: str = Field(
        ..., 
        description="The detailed classification of the primary focus of ADC pre-clinical research (e.g. efficacy, safety, pharmacokinetics, pharmacodynamics, etc.)"
    )

class ADCPreclinicalAnalysis(BaseModel):
    """
    Structures the analysis of publication's title and abstract based *only* on the provided information.
    """
    is_adc_focused: IsADCFocused = Field(
        ...,
        description="A detailed assessment of whether the publication is focused on Antibody-Drug Conjugates (ADCs)."
    )
    discusses_preclinical_experiments: DiscussesPreclinicalExperiments = Field(
        ...,
        description="A detailed assessment of whether the publication involves ADC related pre-clinical experiments in oncology."
    )
    identified_oncology_indications: Optional[List[IdentifiedOncologyIndication]] = Field(
        default_factory=list,
        description="A list of oncology disease indications identified in pre-clinical experiments of ADCs, if any. This list will be empty if no indications are found."
    )
    identified_preclinical_study_focuses: Optional[List[IdentifiedPreclinicalStudyFocus]] = Field(
        default_factory=list,
        description="A list of pre-clinical study focuses identified in this publication that relate to ADCs in oncology. If no focuses are identified, this list will be empty."
    )