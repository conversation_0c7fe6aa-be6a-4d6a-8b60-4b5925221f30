import os, json, traceback, pandas as pd
import requests
from requests.adapters import HTTPA<PERSON>pter
from requests.packages.urllib3.util.retry import Retry
from dataclasses import dataclass
from typing import List, Optional, Literal, Tuple
from pathlib import Path
import zipfile
import logging
from datetime import datetime, timed<PERSON>ta
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import sqlite3
from enum import Enum
from abc import ABC, abstractmethod

class DownloadStatus(Enum):
    SUCCESS = "SUCCESS"
    NOT_AVAILABLE = "NOT_AVAILABLE"
    FAILED = "FAILED"

class DatabaseHandler(ABC):
    """Abstract base class for database operations"""
    
    @abstractmethod
    def init_db(self) -> None:
        """Initialize database with required tables"""
        pass
    
    @abstractmethod
    def update_fulltext_status(self, id: str, pmcid: str, status: DownloadStatus, message: Optional[str] = None, download_path: Optional[str] = None) -> None:
        """Update fulltext download status in database"""
        pass
    
    @abstractmethod
    def update_supplementary_status(self, id: str, pmcid: str, status: DownloadStatus, message: Optional[str] = None, download_path: Optional[str] = None) -> None:
        """Update supplementary files download status in database"""
        pass
    
    @abstractmethod
    def should_retry_fulltext(self, id: str, pmcid: str) -> Tuple[bool, Optional[str]]:
        """Check if fulltext download should be retried"""
        pass
    
    @abstractmethod
    def should_retry_supplementary(self, id: str, pmcid: str) -> Tuple[bool, Optional[str]]:
        """Check if supplementary files download should be retried"""
        pass

class SQLiteHandler(DatabaseHandler):
    """SQLite implementation of database operations"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_db()
    
    def init_db(self) -> None:
        """Initialize SQLite database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create dim_fulltext table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dim_fulltext (
                    id TEXT PRIMARY KEY,
                    pmcid TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    download_path TEXT,
                    last_attempt TIMESTAMP,
                    retry_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create dim_supplementary table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dim_supplementary (
                    id TEXT PRIMARY KEY,
                    pmcid TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    download_path TEXT,
                    last_attempt TIMESTAMP,
                    retry_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def update_fulltext_status(self, id: str, pmcid: str, status: DownloadStatus, message: Optional[str] = None, download_path: Optional[str] = None) -> None:
        """Update fulltext download status in database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO dim_fulltext (id, pmcid, status, message, download_path, last_attempt, retry_count)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 1)
                ON CONFLICT(id) DO UPDATE SET
                    status = ?,
                    message = ?,
                    download_path = ?,
                    last_attempt = CURRENT_TIMESTAMP,
                    retry_count = retry_count + 1,
                    updated_at = CURRENT_TIMESTAMP
            ''', (id, pmcid, status.value, message, download_path, status.value, message, download_path))
            conn.commit()
    
    def update_supplementary_status(self, id: str, pmcid: str, status: DownloadStatus, message: Optional[str] = None, download_path: Optional[str] = None) -> None:
        """Update supplementary files download status in database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO dim_supplementary (id, pmcid, status, message, download_path, last_attempt, retry_count)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 1)
                ON CONFLICT(id) DO UPDATE SET
                    status = ?,
                    message = ?,
                    download_path = ?,
                    last_attempt = CURRENT_TIMESTAMP,
                    retry_count = retry_count + 1,
                    updated_at = CURRENT_TIMESTAMP
            ''', (id, pmcid, status.value, message, download_path, status.value, message, download_path))
            conn.commit()
    
    def should_retry_fulltext(self, id: str, pmcid: str) -> Tuple[bool, Optional[str]]:
        """Check if fulltext download should be retried"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT status, message, retry_count, last_attempt
                FROM dim_fulltext
                WHERE id = ? AND pmcid = ?
            ''', (id, pmcid))
            result = cursor.fetchone()
            
            if not result:
                return True, None
            
            status, message, retry_count, last_attempt = result
            
            if status == DownloadStatus.SUCCESS.value:
                return False, "Already downloaded successfully"
            
            if status == DownloadStatus.NOT_AVAILABLE.value:
                return False, "Not available"
            
            if retry_count >= 3:  # TODO: Make this configurable
                return False, f"Max retries reached: {message}"
            
            # If last attempt was more than 1 hour ago, retry
            if datetime.strptime(last_attempt, '%Y-%m-%d %H:%M:%S') < datetime.now() - timedelta(hours=1):
                return True, None
            
            return False, f"Waiting for retry: {message}"
    
    def should_retry_supplementary(self, id: str, pmcid: str) -> Tuple[bool, Optional[str]]:
        """Check if supplementary files download should be retried"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT status, message, retry_count, last_attempt
                FROM dim_supplementary
                WHERE id = ? AND pmcid = ?
            ''', (id, pmcid))
            result = cursor.fetchone()
            
            if not result:
                return True, None
            
            status, message, retry_count, last_attempt = result
            
            if status == DownloadStatus.SUCCESS.value or status == DownloadStatus.NOT_AVAILABLE.value:
                return False, message
            
            if retry_count >= 3:  # TODO: Make this configurable
                return False, f"Max retries reached: {message}"
            
            # If last attempt was more than 1 hour ago, retry
            if datetime.strptime(last_attempt, '%Y-%m-%d %H:%M:%S') < datetime.now() - timedelta(hours=1):
                return True, None
            
            return False, f"Waiting for retry: {message}"

def setup_logging(logs_dir: str = "logs", logging_level: Literal['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'] = 'WARNING') -> logging.Logger:
    """Configure logging based on verbosity level."""
    # Create logs directory if it doesn't exist
    os.makedirs(logs_dir, exist_ok=True)

    level = getattr(logging, logging_level)

    # Create formatters
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
                                     datefmt='%Y-%m-%d %H:%M:%S')
    
    # Setup handlers
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(console_formatter)
    
    log_file = f'{logs_dir}/europepmc_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.handlers = []
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.info(f"Logging initialized. Log file: {log_file}")
    return root_logger

@dataclass
class PMCArticle:
    id: str
    pmcid: str
    has_fulltext: bool = False
    fulltext_xml: Optional[Path] = None
    has_suppl_files: bool = False
    suppl_files_path: Optional[Path] = None
    fulltext_message: Optional[str] = None
    suppl_files_message: Optional[str] = None

class EuropePMCDownloader:
    """Downloads full text and supplementary materials from Europe PMC using REST APIs"""
    
    BASE_URL = "https://www.ebi.ac.uk/europepmc/webservices/rest"
    
    def __init__(self, timeout: int = 120, download_directory: str="data/publication_files", db_path: str = "europe_pmc.db", logger: logging.Logger = None):
        """
        Initialize the downloader
        
        Args:
            timeout: Timeout in seconds for requests
            download_directory: Base directory for downloads
            db_path: Path to SQLite database file
            logger: Logger instance to use
        """
        self.download_directory = Path(download_directory)
        self.timeout = timeout
        self.session = self._create_session()
        self.logger = logger
        self.db_handler = SQLiteHandler(db_path)
        self.max_retries = 3
        self.logger.info(f"Initialized EuropePMC Downloader. Output directory for downloads: {self.download_directory}")

    def _create_session(self) -> requests.Session:
        """Create and configure requests session with retries"""
        session = requests.Session()
        
        # Configure retry strategy
            # HTTP status codes to retry on:
            # 429: Too Many Requests - Server rate limiting
            # 500: Internal Server Error
            # 502: Bad Gateway
            # 503: Service Unavailable  
            # 504: Gateway Timeout
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def _prepare_article_dirs(self, pmcid: str) -> tuple[Path, Path]:
        """Create directory structure for article downloads"""
        article_dir = self.download_directory / pmcid
        suppl_dir = article_dir / "supplementary_files"
        
        self.logger.debug(f"Creating directory structure for PMCID: {pmcid}")
        article_dir.mkdir(parents=True, exist_ok=True)
        suppl_dir.mkdir(exist_ok=True)
        
        return article_dir, suppl_dir

    def _download_fulltext(self, id: str, pmcid: str, article_dir: Path) -> tuple[Optional[Path], Optional[str]]:
        """Download full text XML using REST API"""
        should_retry, reason = self.db_handler.should_retry_fulltext(id, pmcid)
        if not should_retry:
            self.logger.info(f"Skipping fulltext download for {pmcid}: {reason}")
            return None, reason

        url = f"{self.BASE_URL}/{pmcid}/fullTextXML"
        self.logger.debug(f"Attempting to download fulltext from: {url}")
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 404:
                message = f"No fulltext available for PMCID: {pmcid}"
                self.logger.info(message)
                self.db_handler.update_fulltext_status(id, pmcid, DownloadStatus.NOT_AVAILABLE, message)
                return None, message
                
            response.raise_for_status()
            
            # Save the XML content
            fulltext_path = article_dir / f"{pmcid}.xml"
            
            with open(fulltext_path, "w", encoding="utf-8") as f:
                f.write(response.text)
            
            message = f"Downloaded fulltext for PMCID: {pmcid}"
            self.logger.info(message)
            self.db_handler.update_fulltext_status(id, pmcid, DownloadStatus.SUCCESS, message, str(fulltext_path))
            return fulltext_path, None
            
        except requests.exceptions.HTTPError as e:
            message = f"HTTP error downloading fulltext for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_fulltext_status(id, pmcid, DownloadStatus.FAILED, message)
            return None, message
        except requests.exceptions.ConnectionError as e:
            message = f"Connection error downloading fulltext for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_fulltext_status(id, pmcid, DownloadStatus.FAILED, message)
            return None, message
        except requests.exceptions.Timeout as e:
            message = f"Timeout downloading fulltext for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_fulltext_status(id, pmcid, DownloadStatus.FAILED, message)
            return None, message
        except Exception as e:
            message = f"Unexpected error downloading fulltext for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_fulltext_status(id, pmcid, DownloadStatus.FAILED, message)
            return None, message

    def _download_supplementary(self, id: str, pmcid: str, suppl_dir: Path) -> tuple[bool, Optional[str]]:
        """Download supplementary files using REST API"""
        should_retry, reason = self.db_handler.should_retry_supplementary(id, pmcid)
        if not should_retry:
            self.logger.info(f"Skipping supplementary files download for {pmcid}: {reason}")
            return False, reason

        url = f"{self.BASE_URL}/{pmcid}/supplementaryFiles"
        self.logger.debug(f"Attempting to download supplementary files from: {url}")
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 404:
                message = f"No supplementary files available for PMCID: {pmcid}"
                self.logger.info(message)
                self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.NOT_AVAILABLE, message)
                return False, message
                
            response.raise_for_status()
            
            # Save zip file
            zip_path = suppl_dir / f"{pmcid}_suppl.zip"
            with open(zip_path, "wb") as f:
                f.write(response.content)
            
            self.logger.debug(f"Downloaded supplementary files zip for {pmcid}")
            
            # Extract zip file
            try:
                with zipfile.ZipFile(zip_path) as zf:
                    file_list = zf.namelist()
                    self.logger.debug(f"Extracting {len(file_list)} files from zip for {pmcid}")
                    zf.extractall(suppl_dir)
                
                zip_path.unlink()
                message = f"Successfully extracted supplementary files for PMCID: {pmcid}"
                self.logger.info(message)
                self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.SUCCESS, message, str(suppl_dir))
                return True, None
                
            except zipfile.BadZipFile:
                message = f"Invalid zip file received for {pmcid}"
                self.logger.error(message)
                if zip_path.exists():
                    zip_path.unlink()
                self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.FAILED, message)
                return False, message
                
        except requests.exceptions.HTTPError as e:
            message = f"HTTP error downloading supplementary files for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.FAILED, message)
            return False, message
        except requests.exceptions.ConnectionError as e:
            message = f"Connection error downloading supplementary files for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.FAILED, message)
            return False, message
        except requests.exceptions.Timeout as e:
            message = f"Timeout downloading supplementary files for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.FAILED, message)
            return False, message
        except Exception as e:
            message = f"Unexpected error downloading supplementary files for {pmcid}: {str(e)}"
            self.logger.error(message)
            self.db_handler.update_supplementary_status(id, pmcid, DownloadStatus.FAILED, message)
            return False, message

    def _process_article(self, id: str, pmcid: str) -> PMCArticle:
        """Process a single article - download fulltext and supplementary files"""
        article = PMCArticle(id=id, pmcid=pmcid)
        self.logger.info(f"Processing article: {pmcid}")
        
        try:
            article_dir, suppl_dir = self._prepare_article_dirs(pmcid)
            
            # Download fulltext
            fulltext_path, message = self._download_fulltext(id, pmcid, article_dir)
            if fulltext_path is not None:
                article.has_fulltext = True
                article.fulltext_xml = fulltext_path
                self.logger.debug(f"Saved fulltext to: {fulltext_path}")
            if message:
                article.fulltext_message = message
            
            # Download supplementary files
            has_suppl, message = self._download_supplementary(id, pmcid, suppl_dir)
            if has_suppl:
                article.has_suppl_files = True
                article.suppl_files_path = suppl_dir
            if message:
                article.suppl_files_message = message
                
        except Exception as e:
            message = f"Error processing {pmcid}: {str(e)}"
            self.logger.error(message)
            article.fulltext_message = message
            article.suppl_files_message = message
            
        return article

    def download_articles(self, primary_ids: List[str], pmcids: List[str], batch_size: int = 5) -> List[PMCArticle]:
        """
        Download multiple articles concurrently using ThreadPoolExecutor
        
        Args:
            pmcids: List of PMC IDs to download
            batch_size: Number of concurrent downloads
            
        Returns:
            List of PMCArticle objects with download results
        """
        self.logger.info(f"Starting concurrent download of {len(pmcids)} articles with batch size {batch_size}")
        results = []
        
        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            # Submit all tasks
            future_to_pmcid = {
                executor.submit(self._process_article, id, pmcid): (id, pmcid) 
                for id, pmcid in zip(primary_ids, pmcids)
            }
            
            # Process results as they complete
            for future in tqdm(as_completed(future_to_pmcid), total=len(pmcids), desc="Downloading articles"):
                id, pmcid = future_to_pmcid[future]
                try:
                    article = future.result()
                    results.append(article)
                except Exception as e:
                    self.logger.error(f"Failed to process article {pmcid}: {str(e)}")
                    results.append(PMCArticle(pmcid=pmcid, fulltext_message=str(e), suppl_files_message=str(e)))
        
        successful_fulltext = sum(1 for article in results if article.has_fulltext)
        successful_suppl_files = sum(1 for article in results if article.has_suppl_files)
        self.logger.info(f"Successfully downloaded {successful_fulltext}/{len(pmcids)} articles with fulltext and {successful_suppl_files}/{len(pmcids)} articles with supplementary files")
        
        return results

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()
        if exc_type:
            self.logger.error(f"Error during context exit: {exc_type.__name__}: {exc_val}")

def read_pmcids_from_openalex_db(db_path: str, logger: logging.Logger) -> List[str]:
    """Read PMCIDs from OpenAlex database"""
    if not os.path.exists(db_path):
        logger.error(f"OpenAlex database {db_path} does not exist")
        return []
    
    try:
        query = "SELECT id, ids FROM dim_openalex_works"
        df = pd.read_sql_query(query, sqlite3.connect(db_path))
        logger.info(f"Reading PMCIDs from table dim_openalex_works in OpenAlex database {db_path}")
        
        primary_ids = []
        found_pmcids = []
        missing_pmcids_count = 0
        
        for _, row in df.iterrows():
            id = str(row['id'])
            ids = json.loads(row['ids'])
            if 'pmcid' in ids:
                pmc_link = ids['pmcid'] # e.g. "https://www.ncbi.nlm.nih.gov/pmc/articles/431765"
                pmcid = 'PMC' + pmc_link.split('/')[-1]
                found_pmcids.append(pmcid)
                primary_ids.append(id)
            else:
                missing_pmcids_count += 1
        
        logger.info(f"Found {len(found_pmcids)} PMCIDs in OpenAlex database {db_path}")
        logger.info(f"Found {missing_pmcids_count} PMCIDs in OpenAlex database {db_path} that do not have a PMCID")
        
    except Exception as e:
        logger.error(f"Error reading PMCIDs from OpenAlex database {db_path}: {e}")
        logger.error(f"Error traceback: {traceback.format_exc()}")
        return []
    
    return found_pmcids, primary_ids

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Download full text and supplementary files from Europe PMC")
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--pmcids_db", type=str, default="db/openalex_data.db", help="Path to OpenAlex database")
    group.add_argument("--pmcids_list", type=str, default=None, help="Comma-separated list of PMCIDs to download")
    
    parser.add_argument("--timeout", type=int, default=120, help="Timeout in seconds for each request to Europe PMC API")
    parser.add_argument("--batch-size", type=int, default=5, help="Number of concurrent downloads")
    parser.add_argument("--download-directory", "-dd", type=str, default="data/publication_files", help="Output directory for downloaded files")
    parser.add_argument("--db-path", "-db", type=str, default="db/publication_files.db", help="Path to SQLite database file")
    parser.add_argument("--logs-dir", "-ld", type=str, default="logs", help="Path to logs directory")
    parser.add_argument("--logging-level", "-ll", type=str, default="WARNING", help="Set logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
    args = parser.parse_args()
    
    # Setup logging first
    logger = setup_logging(logs_dir=args.logs_dir, logging_level=args.logging_level)
    logger.info("Starting Europe PMC downloader")
    
    # Example PMCIDs
    if args.pmcids_db:
        pmcids, primary_ids = read_pmcids_from_openalex_db(args.pmcids_db, logger)
    
    if args.pmcids_list:
        pmcids = args.pmcids_list.split(',')
        primary_ids = [f"openalex_{i}" for i in range(len(pmcids))]
    
    # Download all articles
    with EuropePMCDownloader(timeout=args.timeout, download_directory=args.download_directory, db_path=args.db_path, logger=logger) as downloader:
        articles = downloader.download_articles(primary_ids, pmcids, batch_size=args.batch_size)