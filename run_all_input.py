import os
import glob
import subprocess

# Get all .md files in ./output/
md_files = glob.glob(os.path.join('/domino/datasets/local/adc_data_center/markdown_29/', '*.md'))



if not md_files:
    print('No .md files found in ./markdowns_of_interest/')
    exit(0)

for md_file in md_files:
    print(f'Processing {md_file}...')
    cmd = [
        'python',
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        md_file,
        '--output-dir',
        '/domino/datasets/local/adc_data_center/scale_up_itr_2_29'
    ]
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f'Error processing {md_file}, exited with code {result.returncode}')
        break
print('Done.')
