import pyarrow.parquet as pq

parquet_file_path = 'top_europepmc.parquet'  # Update if needed

# Read the Parquet file
table = pq.read_table(parquet_file_path)
df = table.to_pandas()

# Print all column names
print("Columns:", df.columns.tolist())

# Show first 5 rows for each column to help identify the ID column
for col in df.columns:
    print(f"\nSample values from '{col}':")
    print(df[col].head())