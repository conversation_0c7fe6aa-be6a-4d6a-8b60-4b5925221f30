#!/usr/bin/env python3
"""
Convert W4390743855.xml to markdown using the existing xml_to_md.py functions
"""
import sys
import os
from pathlib import Path

# Add the adc_data_center directory to the path to import the xml_to_md module
sys.path.append('/home/<USER>/scp/adc_data_center')

# Import the conversion function from the existing script
from xml_to_md import convert_xml_to_md

def convert_target_xml():
    """Convert W4390743855.xml to markdown and copy to markdowns_of_interest"""
    
    # Define paths
    xml_file = Path('/home/<USER>/scp/xml_from_parquet/W4390743855.xml')
    temp_md_file = Path('/home/<USER>/scp/xml_from_parquet/W4390743855.md')
    target_md_file = Path('/home/<USER>/scp/markdowns_of_interest/w4390743855.md')
    
    print("="*60)
    print("CONVERTING W4390743855.XML TO MARKDOWN")
    print("="*60)
    
    # Check if XML file exists
    if not xml_file.exists():
        print(f"❌ XML file not found: {xml_file}")
        return False
    
    print(f"📁 Source XML: {xml_file}")
    print(f"📄 Target MD: {target_md_file}")
    
    try:
        # Convert XML to markdown using the existing function
        print(f"\n🔄 Converting XML to markdown...")
        convert_xml_to_md(xml_file, temp_md_file)
        
        # Verify the markdown file was created
        if not temp_md_file.exists():
            print(f"❌ Markdown file was not created: {temp_md_file}")
            return False
        
        print(f"✅ Conversion successful!")
        
        # Copy to markdowns_of_interest directory with lowercase filename
        import shutil
        
        # Ensure target directory exists
        target_md_file.parent.mkdir(exist_ok=True)
        
        # Copy the file
        shutil.copy2(temp_md_file, target_md_file)
        print(f"✅ Copied to: {target_md_file}")
        
        # Clean up temporary file
        temp_md_file.unlink()
        print(f"🧹 Cleaned up temporary file")
        
        # Verify final result
        if target_md_file.exists():
            file_size = target_md_file.stat().st_size
            print(f"📊 Final file size: {file_size:,} bytes")
            
            # Show first few lines of the markdown
            content = target_md_file.read_text(encoding='utf-8')
            lines = content.split('\n')[:10]
            print(f"\n📝 First few lines of converted markdown:")
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line[:80]}{'...' if len(line) > 80 else ''}")
            
            return True
        else:
            print(f"❌ Final file not found: {target_md_file}")
            return False
            
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_collection():
    """Verify the updated markdowns_of_interest collection"""
    
    print(f"\n{'='*60}")
    print("VERIFYING MARKDOWNS_OF_INTEREST COLLECTION")
    print("="*60)
    
    markdowns_dir = Path('/home/<USER>/scp/markdowns_of_interest')
    
    if not markdowns_dir.exists():
        print(f"❌ Directory not found: {markdowns_dir}")
        return
    
    md_files = list(markdowns_dir.glob('*.md'))
    print(f"📁 Directory: {markdowns_dir}")
    print(f"📄 Total markdown files: {len(md_files)}")
    
    print(f"\n📋 Files in collection:")
    for md_file in sorted(md_files):
        file_size = md_file.stat().st_size
        print(f"   - {md_file.name} ({file_size:,} bytes)")
    
    # Check if our target file is there
    target_file = markdowns_dir / 'w4390743855.md'
    if target_file.exists():
        print(f"\n✅ W4390743855 successfully added to collection!")
        print(f"   File: {target_file.name}")
        print(f"   Size: {target_file.stat().st_size:,} bytes")
    else:
        print(f"\n❌ W4390743855 not found in collection")

if __name__ == "__main__":
    print("Starting conversion of W4390743855.xml to markdown...")
    
    success = convert_target_xml()
    
    if success:
        verify_collection()
        print(f"\n🎉 SUCCESS: W4390743855 has been converted and added to your markdown collection!")
        print(f"📊 Your collection now has 10 out of 11 target papers!")
    else:
        print(f"\n❌ FAILED: Conversion was not successful")
