#!/usr/bin/env python3
import pandas as pd
import os
from pathlib import Path

def analyze_parquet_file(file_path):
    """Analyze the structure of a parquet file"""
    print(f"\n=== Analyzing {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return None
    
    try:
        df = pd.read_parquet(file_path)
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print()
        print("Data types:")
        print(df.dtypes)
        print()
        print("First few rows:")
        print(df.head())
        print()
        
        # Look for columns that might contain W IDs
        print("=== SEARCHING FOR W ID COLUMNS ===")
        for col in df.columns:
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(5).tolist()
                print(f"{col}: {sample_values}")
                
                # Check if any values match W ID pattern
                w_id_matches = [val for val in sample_values if isinstance(val, str) and val.startswith('W') and val[1:].isdigit()]
                if w_id_matches:
                    print(f"  *** FOUND W IDs in {col}: {w_id_matches}")
        
        return df
        
    except Exception as e:
        print(f"Error reading parquet file: {e}")
        return None

def find_target_w_ids(df, target_ids):
    """Find which target W IDs exist in the dataframe"""
    if df is None:
        return []
    
    print(f"\n=== SEARCHING FOR TARGET W IDs ===")
    print(f"Target IDs: {target_ids}")
    
    found_ids = []
    w_id_column = None
    
    # Find the column containing W IDs
    for col in df.columns:
        if df[col].dtype == 'object':
            sample_values = df[col].dropna().head(10).tolist()
            w_id_matches = [val for val in sample_values if isinstance(val, str) and val.startswith('W') and val[1:].isdigit()]
            if w_id_matches:
                w_id_column = col
                print(f"Using column '{col}' for W ID matching")
                break
    
    if w_id_column is None:
        print("No column with W IDs found!")
        return []
    
    # Check which target IDs are present
    for target_id in target_ids:
        if target_id in df[w_id_column].values:
            found_ids.append(target_id)
            print(f"✓ Found: {target_id}")
        else:
            print(f"✗ Missing: {target_id}")
    
    return found_ids, w_id_column

if __name__ == "__main__":
    # Target W IDs to find
    target_w_ids = [
        'W4292181683', 'W2314772071', 'W4284897854', 'W4226170358',
        'W4226097966', 'W2064424529', 'W4400124600', 'W4393115738'
    ]
    
    # Check all parquet files
    parquet_files = [
        'adc_data_center/top_europepmc.parquet',
        'dry_run_updated/top_europepmc.parquet'
    ]

    found_any = False
    for parquet_file in parquet_files:
        df = analyze_parquet_file(parquet_file)
        if df is not None:
            result = find_target_w_ids(df, target_w_ids)
            if len(result) == 2:  # Check if we got both found_ids and w_id_col
                found_ids, w_id_col = result
                if found_ids:
                    print(f"\nFound {len(found_ids)} target IDs in {parquet_file}")
                    print(f"W ID column: {w_id_col}")
                    found_any = True

    if not found_any:
        print("No target W IDs found in any parquet file!")

        # Let's also check what W IDs are actually in the files
        print("\n=== SAMPLE W IDs FROM PARQUET FILES ===")
        for parquet_file in parquet_files:
            if os.path.exists(parquet_file):
                df = pd.read_parquet(parquet_file)
                if 'oa_works_id' in df.columns:
                    sample_ids = df['oa_works_id'].dropna().head(10).tolist()
                    print(f"{parquet_file}: {sample_ids}")
