# Antibody-Drug-Conjugates Data Center

A Python-based data processing and analysis system for handling academic publications and research data specifically for Antibody-Drug Conjugates (ADC)

## Overview

This project provides tools for downloading, processing, and analyzing academic publications from various sources including Europe PMC and OpenAlex. It includes functionality for publication profiling and data management.

## Features

- Publication data downloading from Europe PMC and OpenAlex
- Publication profiling and analysis
- Asynchronous data processing
- Local database storage
- Progress tracking and logging
- Automated pipeline execution

## Project Structure

```
.
├── config/          # Configuration files
│   └── openalex_query.json  # OpenAlex API query configuration
├── data/           # Data storage
│   └── publication_files/   # Downloaded publication files
├── db/             # Database files
│   ├── openalex_data.db     # OpenAlex publication data
│   ├── publication_files.db # Europe PMC download status
│   └── publication_profiles.db # Publication analysis results
├── docs/           # Documentation
├── logs/           # Application logs
├── prompts/        # AI prompt templates
├── src/            # Source code
│   ├── utils/      # Utility functions
│   ├── europe_pmc_downloader.py
│   ├── openalex_handler.py
│   ├── publication_profiler.py
│   └── main.sh     # Main execution script
├── tests/          # Test files
├── .env            # Environment variables
└── requirements.txt # Python dependencies
```

## Prerequisites

- Python 3.8+
- pip
- bash shell

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd adc_data_center
```

2. Create and activate a virtual environment (recommended):
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
   - Copy `.env.example` to `.env` (if available)
   - Configure necessary API keys and settings in `.env`

5. Create configuration file:
   - Create `config/openalex_query.json` with your OpenAlex API query parameters

## Usage

The project provides a main script (`src/main.sh`) that executes the entire pipeline in sequence:

1. **OpenAlex Handler**: Downloads publication data from OpenAlex
2. **Europe PMC Downloader**: Downloads full text and supplementary files from Europe PMC
3. **Publication Profiler**: Analyzes and profiles the downloaded publications

To run the complete pipeline:

```bash
./src/main.sh
```

The script will:
- Create necessary directories (db, data, logs)
- Execute each component in sequence
- Log progress to both console and log files
- Stop if any component fails
- Provide completion status

### Individual Components

1. **OpenAlex Handler** (`openalex_handler.py`):
   - Interfaces with OpenAlex API
   - Processes and stores academic publication data
   - Requires `config/openalex_query.json` for API queries

2. **Europe PMC Downloader** (`europe_pmc_downloader.py`):
   - Downloads publication data from Europe PMC
   - Handles batch processing and rate limiting
   - Uses OpenAlex database to identify publications

3. **Publication Profiler** (`publication_profiler.py`):
   - Analyzes and profiles publications
   - Generates insights from publication data
   - Uses both OpenAlex and Europe PMC data

## Configuration

Configuration files are located in the `config/` directory. Make sure to set up:

1. `config/openalex_query.json`: OpenAlex API query parameters
2. `.env`: Environment variables for API keys and settings

## Testing

Run tests using pytest:
```bash
pytest tests/
```

## Logging

Log files are stored in the `logs/` directory with timestamps. The main script creates a new log file for each run with the format `main_YYYYMMDD_HHMMSS.log`.