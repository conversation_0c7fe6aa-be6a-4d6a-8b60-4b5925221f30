#!/usr/bin/env python3
"""
Two-step process for XML files:
1. Rename XML files to remove source suffixes, handling conflicts with priority
2. Convert renamed XML files to Markdown format

Priority order for source conflicts:
1. top_reviews (highest priority)
2. top_articles 
3. top_europepmc (lowest priority)
"""

import os
import shutil
from pathlib import Path
from collections import defaultdict
import json
from datetime import datetime

# Import the XML to Markdown conversion functions
import sys
sys.path.append('adc_data_center')
from xml_to_md import convert_xml_to_md

def analyze_xml_files(xml_dir):
    """Analyze XML files to understand source distribution and conflicts"""
    xml_path = Path(xml_dir)
    files = list(xml_path.glob("*.xml"))
    
    # Group files by work ID
    work_id_groups = defaultdict(list)
    
    for file in files:
        if file.name == "extraction_report.json":
            continue
            
        # Extract work ID and source from filename
        # Format: W{numbers}_{source}.xml
        parts = file.stem.split('_')
        if len(parts) >= 2:
            work_id = parts[0]  # W{numbers}
            source = '_'.join(parts[1:])  # everything after first underscore
            work_id_groups[work_id].append((file, source))
    
    return work_id_groups

def prioritize_sources(sources_list):
    """
    Prioritize sources based on preference order:
    1. top_reviews (highest priority)
    2. top_articles
    3. top_europepmc (lowest priority)
    """
    priority_order = ['top_reviews', 'top_articles', 'top_europepmc']
    
    # Sort by priority (lower index = higher priority)
    sources_list.sort(key=lambda x: priority_order.index(x[1]) if x[1] in priority_order else 999)
    
    return sources_list[0]  # Return highest priority source

def rename_xml_files(xml_dir, backup_dir="xml_backup"):
    """
    Step 1: Rename XML files to remove source suffixes
    Handle conflicts by prioritizing sources
    """
    print("="*80)
    print("STEP 1: RENAMING XML FILES")
    print("="*80)
    
    xml_path = Path(xml_dir)
    backup_path = Path(backup_dir)
    
    # Create backup directory
    backup_path.mkdir(exist_ok=True)
    
    # Analyze files
    work_id_groups = analyze_xml_files(xml_dir)
    
    rename_report = {
        'timestamp': datetime.now().isoformat(),
        'total_work_ids': len(work_id_groups),
        'conflicts_resolved': 0,
        'files_renamed': 0,
        'files_backed_up': 0,
        'priority_choices': [],
        'single_source_files': [],
        'conflict_resolutions': []
    }
    
    print(f"📊 Found {len(work_id_groups)} unique work IDs")
    
    # Process each work ID group
    for work_id, sources in work_id_groups.items():
        if len(sources) == 1:
            # Single source - simple rename
            old_file, source = sources[0]
            new_file = xml_path / f"{work_id}.xml"
            
            # Backup original
            backup_file = backup_path / old_file.name
            shutil.copy2(old_file, backup_file)
            rename_report['files_backed_up'] += 1
            
            # Rename
            old_file.rename(new_file)
            rename_report['files_renamed'] += 1
            rename_report['single_source_files'].append({
                'work_id': work_id,
                'source': source,
                'old_name': old_file.name,
                'new_name': new_file.name
            })
            
            print(f"✅ {work_id}: {old_file.name} → {new_file.name}")
            
        else:
            # Multiple sources - resolve conflict
            rename_report['conflicts_resolved'] += 1
            
            # Prioritize sources
            chosen_file, chosen_source = prioritize_sources(sources)
            
            # Backup all files
            for file, source in sources:
                backup_file = backup_path / file.name
                shutil.copy2(file, backup_file)
                rename_report['files_backed_up'] += 1
            
            # Rename chosen file
            new_file = xml_path / f"{work_id}.xml"
            chosen_file.rename(new_file)
            rename_report['files_renamed'] += 1
            
            # Remove other files
            for file, source in sources:
                if file != chosen_file and file.exists():
                    file.unlink()
            
            conflict_info = {
                'work_id': work_id,
                'chosen_source': chosen_source,
                'available_sources': [source for _, source in sources],
                'chosen_file': chosen_file.name,
                'new_name': new_file.name
            }
            
            rename_report['conflict_resolutions'].append(conflict_info)
            rename_report['priority_choices'].append(chosen_source)
            
            print(f"🔄 {work_id}: Chose {chosen_source} from {[s for _, s in sources]}")
            print(f"   {chosen_file.name} → {new_file.name}")
    
    # Save rename report
    report_file = xml_path / 'rename_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(rename_report, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n📋 RENAME SUMMARY:")
    print(f"   Total work IDs processed: {rename_report['total_work_ids']}")
    print(f"   Files renamed: {rename_report['files_renamed']}")
    print(f"   Conflicts resolved: {rename_report['conflicts_resolved']}")
    print(f"   Files backed up: {rename_report['files_backed_up']}")
    print(f"   Backup directory: {backup_path.absolute()}")
    print(f"   Detailed report: {report_file}")
    
    # Show priority distribution
    if rename_report['priority_choices']:
        from collections import Counter
        priority_counts = Counter(rename_report['priority_choices'])
        print(f"\n🎯 SOURCE PRIORITY CHOICES:")
        for source, count in priority_counts.items():
            print(f"   {source}: {count} files")
    
    return rename_report

def convert_xml_to_markdown(xml_dir, markdown_dir="markdown"):
    """
    Step 2: Convert renamed XML files to Markdown format
    """
    print("\n" + "="*80)
    print("STEP 2: CONVERTING XML TO MARKDOWN")
    print("="*80)
    
    xml_path = Path(xml_dir)
    md_path = Path(markdown_dir)
    
    # Create markdown directory
    md_path.mkdir(exist_ok=True)
    
    # Find all XML files (excluding reports)
    xml_files = [f for f in xml_path.glob("*.xml") if not f.name.endswith('_report.xml')]
    
    conversion_report = {
        'timestamp': datetime.now().isoformat(),
        'total_xml_files': len(xml_files),
        'successful_conversions': 0,
        'failed_conversions': 0,
        'conversion_details': [],
        'failed_files': []
    }
    
    print(f"📄 Found {len(xml_files)} XML files to convert")
    print(f"📁 Output directory: {md_path.absolute()}")
    
    # Convert each XML file
    for xml_file in xml_files:
        work_id = xml_file.stem
        md_file = md_path / f"{work_id.lower()}.md"  # Use lowercase for consistency
        
        try:
            print(f"\n🔄 Converting {xml_file.name}...")
            
            # Use the existing conversion function
            convert_xml_to_md(xml_file, md_file)
            
            conversion_report['successful_conversions'] += 1
            conversion_report['conversion_details'].append({
                'work_id': work_id,
                'xml_file': xml_file.name,
                'md_file': md_file.name,
                'status': 'success'
            })
            
            print(f"✅ Successfully converted: {md_file.name}")
            
        except Exception as e:
            print(f"❌ Failed to convert {xml_file.name}: {e}")
            conversion_report['failed_conversions'] += 1
            conversion_report['failed_files'].append({
                'work_id': work_id,
                'xml_file': xml_file.name,
                'error': str(e)
            })
    
    # Save conversion report
    report_file = md_path / 'conversion_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(conversion_report, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n📋 CONVERSION SUMMARY:")
    print(f"   Total XML files: {conversion_report['total_xml_files']}")
    print(f"   Successful conversions: {conversion_report['successful_conversions']}")
    print(f"   Failed conversions: {conversion_report['failed_conversions']}")
    print(f"   Success rate: {conversion_report['successful_conversions']/conversion_report['total_xml_files']*100:.1f}%")
    print(f"   Markdown directory: {md_path.absolute()}")
    print(f"   Detailed report: {report_file}")
    
    if conversion_report['failed_files']:
        print(f"\n❌ FAILED CONVERSIONS:")
        for failed in conversion_report['failed_files']:
            print(f"   {failed['xml_file']}: {failed['error']}")
    
    return conversion_report

def main():
    """Main function to execute both steps"""
    xml_directory = "extracted_xml_comprehensive"
    markdown_directory = "markdown"
    backup_directory = "xml_backup"
    
    print("🚀 STARTING TWO-STEP XML PROCESSING")
    print(f"📂 XML Directory: {xml_directory}")
    print(f"📂 Markdown Directory: {markdown_directory}")
    print(f"📂 Backup Directory: {backup_directory}")
    
    # Check if XML directory exists
    if not Path(xml_directory).exists():
        print(f"❌ XML directory not found: {xml_directory}")
        return
    
    try:
        # Step 1: Rename XML files
        rename_report = rename_xml_files(xml_directory, backup_directory)
        
        # Step 2: Convert to Markdown
        conversion_report = convert_xml_to_markdown(xml_directory, markdown_directory)
        
        # Final summary
        print("\n" + "="*80)
        print("🎉 PROCESS COMPLETED SUCCESSFULLY!")
        print("="*80)
        print(f"✅ Step 1 - Renamed {rename_report['files_renamed']} XML files")
        print(f"✅ Step 2 - Converted {conversion_report['successful_conversions']} files to Markdown")
        print(f"📁 XML files preserved in: {xml_directory}")
        print(f"📁 Markdown files created in: {markdown_directory}")
        print(f"📁 Original files backed up in: {backup_directory}")
        
        if conversion_report['failed_conversions'] > 0:
            print(f"⚠️  {conversion_report['failed_conversions']} conversions failed - check reports for details")
        
    except Exception as e:
        print(f"❌ Process failed: {e}")
        raise

if __name__ == "__main__":
    main()
