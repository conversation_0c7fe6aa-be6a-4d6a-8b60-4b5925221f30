# Parquet File Search Results for Target W IDs

## Search Summary
Searched `top_europepmc.parquet` using the `oa_works_id` column for the 11 target W IDs from your markdown search.

## Target W IDs Searched
1. W4390743855
2. W2314772071
3. W4226170358
4. W4376958886
5. W4226097966
6. W3093537091
7. W4280631979
8. W4284897854
9. W4400124600
10. W4387230410
11. W2911746982

## Parquet File Analysis

### File Details
- **File**: `adc_data_center/top_europepmc.parquet`
- **Shape**: 868 rows × 44 columns
- **ID Column**: `oa_works_id` (868 non-null values)
- **XML Column**: `rawContent` (contains XML data)

## Search Results

### ✅ Found in Parquet (5 out of 11)

| W ID | Status | XML Extracted |
|------|--------|---------------|
| W4390743855 | ✅ Found | ✅ W4390743855.xml |
| W3093537091 | ✅ Found | ✅ W3093537091.xml |
| W4280631979 | ✅ Found | ✅ W4280631979.xml |
| W4387230410 | ✅ Found | ✅ W4387230410.xml |
| W2911746982 | ✅ Found | ✅ W2911746982.xml |

### ❌ Missing from Parquet (6 out of 11)

| W ID | Status | Notes |
|------|--------|-------|
| W2314772071 | ❌ Not Found | Not in parquet dataset |
| W4226097966 | ❌ Not Found | Not in parquet dataset |
| W4226170358 | ❌ Not Found | Not in parquet dataset |
| W4284897854 | ❌ Not Found | Not in parquet dataset |
| W4376958886 | ❌ Not Found | Not in parquet dataset |
| W4400124600 | ❌ Not Found | Not in parquet dataset |

## XML Extraction Results

### Successfully Extracted XML Files
Created directory: `xml_from_parquet/`

The following XML files were extracted from the parquet data:
```
xml_from_parquet/
├── W2911746982.xml
├── W3093537091.xml
├── W4280631979.xml
├── W4387230410.xml
└── W4390743855.xml
```

## Cross-Reference with Previous Searches

### Comparison with Markdown Files Found
From the previous markdown search in `output_4.1/`, we found 9 files.
From the parquet search, we found 5 files.

**Overlap Analysis:**
- **W4390743855**: ❌ Not found in markdown, ✅ Found in parquet
- **W3093537091**: ✅ Found in markdown, ✅ Found in parquet  
- **W4280631979**: ✅ Found in markdown, ✅ Found in parquet
- **W4387230410**: ✅ Found in markdown, ✅ Found in parquet
- **W2911746982**: ✅ Found in markdown, ✅ Found in parquet

### Comparison with XML Files Found
From the previous XML search in `extractions updated/xml_files/`, we found 7 files.
From the parquet search, we found 5 files.

**Overlap Analysis:**
- **W4390743855**: ❌ Not found in xml_files, ✅ Found in parquet
- **W3093537091**: ❌ Not found in xml_files, ✅ Found in parquet
- **W4280631979**: ❌ Not found in xml_files, ✅ Found in parquet
- **W4387230410**: ❌ Not found in xml_files, ✅ Found in parquet
- **W2911746982**: ❌ Not found in xml_files, ✅ Found in parquet

## Summary Statistics

### 📊 Overall Results
- **Total target IDs**: 11
- **Found in parquet**: 5 (45.5%)
- **Missing from parquet**: 6 (54.5%)
- **XML files extracted**: 5

### 🎯 Key Findings
1. **W4390743855** was found in parquet but not in the markdown files
2. **4 IDs overlap** between markdown and parquet: W3093537091, W4280631979, W4387230410, W2911746982
3. **None of the parquet IDs** were found in the previous xml_files directory
4. The parquet file contains a **different subset** of papers than the other sources

### 📁 Available Data Sources Summary
- **Markdown files** (`output_4.1/`): 9 found
- **XML files** (`extractions updated/xml_files/`): 7 found  
- **Parquet XML** (`xml_from_parquet/`): 5 found
- **Total unique papers with data**: 15 different W IDs across all sources

## Recommendations
1. The 5 XML files extracted from parquet provide additional data not available in other sources
2. Consider checking other parquet files or datasets for the 6 missing IDs
3. The parquet data appears to be a different collection than the markdown and xml_files directories
