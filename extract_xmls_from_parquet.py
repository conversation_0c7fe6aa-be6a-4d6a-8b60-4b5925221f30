import os
import pyarrow.parquet as pq

# Replace <parquet_file_path> with the actual file path
parquet_file_path = 'top_europepmc.parquet'

ids_to_find = [
    "W4390743855",
    "W2314772071",
    "W4226170358",
    "W4376958886",
    "W4226097966",
    "W3093537091",
    'W4280631979',
    "W4284897854",
    "W4400124600",
    "W4387230410",
    "W2911746982"
]

# Create xml output directory if not exists
output_dir = 'xml'
os.makedirs(output_dir, exist_ok=True)

# Read parquet file
table = pq.read_table(parquet_file_path)
df = table.to_pandas()

# Filter rows with matching oa_works_id
filtered_df = df[df['oa_works_id'].isin(ids_to_find)]

# Save each xml to a separate file
for _, row in filtered_df.iterrows():
    file_path = os.path.join(output_dir, f"{row['oa_works_id']}.xml")
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(row['rawContent'])