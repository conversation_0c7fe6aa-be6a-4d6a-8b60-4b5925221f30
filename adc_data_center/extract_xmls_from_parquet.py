import os
import pyarrow.parquet as pq
import xml.etree.ElementTree as ET

def process_parquet_to_xml():
    # Configuration
    parquet_file_path = 'zs_oa_adc_curated_annotated_with_content_europepmc_raw_xml_2025.03-05.parquet'
    ids_to_find = ["W2099808918","w2099808918"]
    output_dir = 'output'
    
    # 1. File existence check
    if not os.path.exists(parquet_file_path):
        raise FileNotFoundError(f"Parquet file not found: {parquet_file_path}")
    
    # 2. Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 3. Read parquet file
        table = pq.read_table(parquet_file_path)
        df = table.to_pandas()
        
        # 4. Verify required columns exist
        required_columns = ['oa_works_id', 'rawContent']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"Available columns: {list(df.columns)}")
            raise KeyError(f"Missing required columns: {missing_columns}")
        
        # 5. Data validation
        print(f"DataFrame shape: {df.shape}")
        print(f"oa_works_id column type: {df['oa_works_id'].dtype}")
        print(f"rawContent column type: {df['rawContent'].dtype}")
        
        # 6. Check for matching IDs
        available_ids = set(df['oa_works_id'].dropna().astype(str))
        found_ids = [id_val for id_val in ids_to_find if str(id_val) in available_ids]
        missing_ids = [id_val for id_val in ids_to_find if str(id_val) not in available_ids]
        
        print(f"IDs to find: {ids_to_find}")
        print(f"IDs found in dataset: {found_ids}")
        if missing_ids:
            print(f"Warning: IDs missing from dataset: {missing_ids}")
        
        # 7. Filter rows
        filtered_df = df[df['oa_works_id'].isin(ids_to_find)]
        print(f"Filtered DataFrame shape: {filtered_df.shape}")
        
        if filtered_df.empty:
            print("No matching rows found. Check your IDs and column values.")
            return
        
        # 8. Process and save XML files
        success_count = 0
        for _, row in filtered_df.iterrows():
            try:
                # Validate XML content
                if pd.isna(row['rawContent']) or row['rawContent'] == '':
                    print(f"Empty rawContent for ID {row['oa_works_id']}")
                    continue
                
                # Test XML parsing
                ET.fromstring(row['rawContent'])
                
                # Write to file
                file_path = os.path.join(output_dir, f"{row['oa_works_id']}.xml")
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(row['rawContent'])
                
                print(f"Successfully wrote: {file_path}")
                success_count += 1
                
            except ET.ParseError as e:
                print(f"Invalid XML content for ID {row['oa_works_id']}: {e}")
            except Exception as e:
                print(f"Error processing ID {row['oa_works_id']}: {e}")
        
        print(f"Processing complete. {success_count} files written successfully.")
        
    except Exception as e:
        print(f"Error reading parquet file: {e}")
        raise

# Run the function
if __name__ == "__main__":
    process_parquet_to_xml()