import argparse
import pandas as pd
import asyncio
import os
import sqlite3
import logging
import json
from typing import List, Literal, Dict
from openai import AsyncAzureOpenAI
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from tqdm import tqdm
from dotenv import load_dotenv
from datetime import datetime
from pathlib import Path
from jinja2 import Template
from src.utils.profiling_pydantic_models import ADCPreclinicalAnalysis
# TODO: Test and enable MLflow
# import mlflow

# Load environment variables
load_dotenv()

# # Configure MLflow
# mlflow.set_tracking_uri("http://localhost:5000")
# mlflow.set_experiment("publication_profiler")

# # Enable MLflow autologging
# mlflow.openai.autolog()

# Configure logging
def setup_logging(logs_dir: str = "logs", log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"):
    """Set up logging configuration with both file and console handlers."""
    # Create logs directory if it doesn't exist
    log_path = Path(logs_dir)
    log_path.mkdir(exist_ok=True)
    
    # Create a timestamped log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_path / f"publication_profiler_{timestamp}.log"

    # set log level
    log_level = logging.getLevelName(log_level)
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def setup_database(db_path: str = "db/publication_profiles.db"):
    """Set up SQLite database and create tables if they don't exist."""
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir)
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS fact_publication_analysis (
            id TEXT PRIMARY KEY,
            title TEXT,
            abstract TEXT,
            adc_focus_reasoning TEXT,
            adc_focus_confidence TEXT,
            adc_focus_final_assessment BOOLEAN,
            adc_names TEXT,
            preclinical_reasoning TEXT,
            preclinical_confidence TEXT,
            preclinical_final_assessment BOOLEAN,
            status INTEGER,
            message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS fact_cancer_indications (
            id TEXT,
            indication_reasoning TEXT,
            indication_confidence TEXT,
            cancer_type TEXT,
            cancer_subtype TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (id) REFERENCES fact_publication_analysis(id)
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS fact_research_objectives (
            id TEXT,
            study_focus_reasoning TEXT,
            study_focus_confidence TEXT,
            primary_focus TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (id) REFERENCES fact_publication_analysis(id)
        )
    ''')
    
    conn.commit()
    return conn

class PublicationProfilerAgent:
    def __init__(self, 
            logger=None, 
            db_path: str = "db/publication_profiles.db", 
            model_params: Dict = {
                'deployment_name': 'gpt-4o',
                'model_name': 'gpt-4o',
                'temperature': 0.0,
                'top_p': 1.0,
                'max_tokens': None,
                'system_prompt_path': "prompts/profiling_system_prompt.md",
                'user_prompt_path': "prompts/profiling_user_prompt.md"
            }
        ):
        self.logger = logger or setup_logging()
        self.db_path = db_path
        self.conn = setup_database(db_path)
        self.model_params = model_params
        self.allowed_status_codes = [200, 400] # REFERENCE --> 200: SUCCESS, 400: CONTENT POLICY ERROR

        # Pop azure parameters from the model_params dictionary
        self.azure_deployment_name = self.model_params.pop("deployment_name")
        self.azure_model_name = self.model_params.pop("model_name")
        self.max_retries = self.model_params.pop("max_retries")

        # Load system and user prompts
        self.system_prompt_path = self.model_params.pop('system_prompt_path')
        self.user_prompt_path = self.model_params.pop('user_prompt_path')

    def _get_processed_publications(self) -> set:
        """Get set of publication IDs that have already been processed."""
        cursor = self.conn.cursor()
        cursor.execute(f"SELECT id FROM fact_publication_analysis WHERE status in ({', '.join(map(str, self.allowed_status_codes))})")
        idset = {row[0] for row in cursor.fetchall()}
        cursor.close()
        return idset
    
    def _save_results(self, results: List[Dict]):
        """Save results to SQLite database."""
        cursor = self.conn.cursor()
        
        for result in results:
            if result["result"] is None:
                # Handle error case
                cursor.execute('''
                    INSERT OR REPLACE INTO fact_publication_analysis 
                    (id, title, abstract, status, message) 
                    VALUES (?, ?, ?, ?, ?)
                ''', (result["id"], result["title"], result["abstract"], result["status"], result["message"]))
            else:
                # Convert ADC names list to JSON string
                adc_names = json.dumps(result["result"].is_adc_focused.adc_names) if result["result"].is_adc_focused.adc_names else None
                
                # Save preclinical results
                cursor.execute('''
                    INSERT OR REPLACE INTO fact_publication_analysis 
                    (id, title, abstract, 
                     adc_focus_reasoning, adc_focus_confidence, adc_focus_final_assessment, adc_names,
                     preclinical_reasoning, preclinical_confidence, preclinical_final_assessment,
                     status, message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    result["id"], 
                    result["title"], 
                    result["abstract"],
                    result["result"].is_adc_focused.reasoning,
                    result["result"].is_adc_focused.confidence,
                    result["result"].is_adc_focused.final_assessment,
                    adc_names,
                    result["result"].discusses_preclinical_experiments.reasoning,
                    result["result"].discusses_preclinical_experiments.confidence,
                    result["result"].discusses_preclinical_experiments.final_assessment,
                    result["status"],
                    result["message"]
                ))
                
                # Delete existing indications and research objectives for this publication
                cursor.execute('DELETE FROM fact_cancer_indications WHERE id = ?', (result["id"],))
                cursor.execute('DELETE FROM fact_research_objectives WHERE id = ?', (result["id"],))
                
                # Save cancer indications
                if result["result"].identified_oncology_indications:
                    for indication in result["result"].identified_oncology_indications:
                        cursor.execute('''
                            INSERT INTO fact_cancer_indications 
                            (id, indication_reasoning, indication_confidence, cancer_type, cancer_subtype)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (
                            result["id"],
                            indication.reasoning,
                            indication.confidence,
                            indication.cancer_type,
                            indication.cancer_subtype,
                        ))
                
                # Save research objectives
                if result["result"].identified_preclinical_study_focuses:
                    for focus in result["result"].identified_preclinical_study_focuses:
                        cursor.execute('''
                            INSERT INTO fact_research_objectives 
                            (id, study_focus_reasoning, study_focus_confidence, primary_focus)
                            VALUES (?, ?, ?, ?)
                        ''', (
                            result["id"],
                            focus.reasoning,
                            focus.confidence,
                            focus.primary_focus,
                        ))
        
        self.conn.commit()
        cursor.close()

    def _initialize_agent(self, publication_type: str):
        self.logger.debug("Initializing Azure OpenAI agent")
        self.logger.debug(f"Azure OpenAI endpoint: {os.getenv('AZURE_OPENAI_ENDPOINT')}")
        self.logger.debug(f"Azure OpenAI deployment: {os.getenv('AZURE_GPT_4O')}")
        self.logger.debug(f"Azure OpenAI API key: {os.getenv('AZURE_OPENAI_API_KEY')}")
        self.logger.debug(f"Azure OpenAI API version: {os.getenv('AZURE_OPENAI_API_VERSION')}")

        # Initialize Azure OpenAI client
        client = AsyncAzureOpenAI(
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            azure_deployment=self.azure_deployment_name,
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
        )
        # Initialize the OpenAI model with Azure provider
        model = OpenAIModel(model_name=self.azure_model_name, provider=OpenAIProvider(openai_client=client))

        # Initialize the Pydantic AI agent with the system and user prompts
        agent = Agent(
            model=model,
            system_prompt=self._load_system_prompt(publication_type),
            result_type=ADCPreclinicalAnalysis,
            retries=self.max_retries,
            result_retries=self.max_retries,
        )
        self.logger.debug("Azure OpenAI based Pydantic AI agent initialized successfully!")
        return agent

    def _load_system_prompt(self, publication_type: str) -> str:
        """Load the system prompt from the file."""
        with open(Template(self.system_prompt_path).render(PUBLICATION_TYPE=publication_type), "r") as f:
            return f.read()
    
    def _load_user_prompt(self, publication_type: str) -> str:
        """Load the user prompt template from the file."""
        with open(Template(self.user_prompt_path).render(PUBLICATION_TYPE=publication_type), "r") as f:
            return f.read()
    
    def _format_user_prompt(self, title: str, abstract: str, publication_type: str) -> str:
        """Format the user prompt with the given title and abstract."""
        template = self._load_user_prompt(publication_type)
        template = Template(template)
        return template.render(TITLE=title, ABSTRACT=abstract)
    
    async def analyze_publication(self, title: str, abstract: str, publication_id: str, publication_type: str) -> Dict:
        """Analyze a single publication's title and abstract."""
        self.logger.debug(f"Analyzing publication: {publication_id}")
        agent = self._initialize_agent(publication_type)
        user_prompt = self._format_user_prompt(title, abstract, publication_type)
        try:
            result = await agent.run(user_prompt, model_settings=self.model_params)
            self.logger.debug(f"Analysis completed for publication: {publication_id}.")
            return {"id": publication_id, "title": title, "abstract": abstract, "result": result.data, "status": 200, "message": "SUCCESS"}
        except Exception as e:
            self.logger.error(f"Error analyzing publication {publication_id}.")
            self.logger.error(f"Error: {e}")
            if hasattr(e, 'status_code') and hasattr(e, 'body') and hasattr(e.body, 'message'):
                return {"id": publication_id, "title": title, "abstract": abstract, "result": None, "status": e.status_code, "message": e.body.get("message")}
            else:
                return {"id": publication_id, "title": title, "abstract": abstract, "result": None, "status": 500, "message": "<Unknown Error>"}
    
    async def analyze_dataframe(self, df: pd.DataFrame, max_concurrent: int = 5) -> List[Dict]:
        """
        Analyze multiple publications from a DataFrame maintaining a continuous pool of concurrent tasks.
        Skip publications that have already been processed.
        Process results as they complete rather than waiting for entire batch.
        """
        self.logger.debug(f"Starting analysis of {len(df)} publications with max concurrent tasks: {max_concurrent}")
        if not all(col in df.columns for col in ['title', 'abstract', 'id', 'type']):
            self.logger.error("DataFrame missing required columns: 'title', 'abstract', 'id' and 'type'")
            raise ValueError("DataFrame must contain 'title', 'abstract', 'id' and 'type' columns")
        
        # Get already processed publications
        processed_ids = self._get_processed_publications()
        
        # Filter out already processed publications
        df = df[~df['id'].isin(processed_ids)]
        self.logger.warning(f"Found {len(processed_ids)} already processed publications. Processing {len(df)} new publications.")
        
        if len(df) == 0:
            self.logger.info("No new publications to process.")
            return []

        # Create a queue to manage publications to process
        queue = asyncio.Queue()
        for _, row in df.iterrows():
            queue.put_nowait((row['title'], row['abstract'], row['id'], row['type']))

        all_results = []
        active_tasks = set()
        
        with tqdm(total=len(df), desc="Analyzing publications") as pbar:
            while not queue.empty() or active_tasks:
                # Start new tasks if we have capacity and items in queue
                while len(active_tasks) < max_concurrent and not queue.empty():
                    title, abstract, pub_id, pub_type = await queue.get()
                    task = asyncio.create_task(self.analyze_publication(title, abstract, pub_id, pub_type))
                    active_tasks.add(task)
                    task.add_done_callback(active_tasks.discard)

                if not active_tasks:
                    break

                # Wait for at least one task to complete
                done, _ = await asyncio.wait(active_tasks, return_when=asyncio.FIRST_COMPLETED)
                
                # Process completed tasks
                for task in done:
                    try:
                        result = await task
                        all_results.append(result)
                        # Save individual result immediately
                        self._save_results([result])
                        pbar.update(1)
                        self.logger.debug(f"Completed and saved result for publication {result['id']}")
                    except Exception as e:
                        self.logger.error(f"Error processing task: {e}")
        
        self.logger.debug(f"Analysis completed for all {len(df)} publications")
        return all_results

def fetch_publication_data_from_db(logger, openalex_db_path):
    """Fetch all publication data from openalex_db."""
    try:
        conn = sqlite3.connect(openalex_db_path)
        
        # Get total count of records with type filter
        count_query = """
            SELECT COUNT(*) 
            FROM dim_openalex_works 
            WHERE abstract IS NOT NULL 
            AND type IN ('review', 'article')
        """
        total_records = pd.read_sql_query(count_query, conn).iloc[0, 0]
        logger.info(f"Total records in openalex_db with type 'review' or 'article' and non-null abstract: {total_records}")
        
        # Fetch data in batches to handle large datasets
        batch_size = 1000
        openalex_data = []
        
        for offset in range(0, total_records, batch_size):
            query = f"""
                SELECT id, title, abstract, type 
                FROM dim_openalex_works 
                WHERE abstract IS NOT NULL 
                AND type IN ('review', 'article')
                LIMIT {batch_size} OFFSET {offset}
            """
            batch_data_df = pd.read_sql_query(query, conn)
            openalex_data.append(batch_data_df)
            logger.info(f"Fetched batch of {len(batch_data_df)} records. Progress: {offset + len(batch_data_df)}/{total_records}")
        
        openalex_data = pd.concat(openalex_data, axis=0, ignore_index=True)
        logger.info(f"Retrieved {len(openalex_data)} publication data from openalex_db")
        
    except Exception as e:
        import traceback
        logger.error(f"Error retrieving publication data from openalex_db: {e}")
        logger.error(f"Error traceback: {traceback.format_exc()}")
        openalex_data = pd.DataFrame()
    finally:
        conn.close()

    return openalex_data

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze ADC preclinical screening data")
    parser.add_argument("--local_data_path", type=str, default=None, help="Path to local datafile")
    parser.add_argument("--openalex_db_path", type=str, default="db/openalex_data.db", help="Path to OpenAlex database")
    parser.add_argument("--max_concurrent", type=int, default=50, help="Maximum number of concurrent tasks")
    parser.add_argument("--db_path", type=str, default="db/publication_profiles.db", help="Path to SQLite database")
    parser.add_argument("--logs-dir", "-ld", type=str, default="logs", help="Path to logs directory")
    parser.add_argument("--logging-level", "-ll", type=str, default="INFO", help="Set logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
    args = parser.parse_args()

    # Set up logging
    logger = setup_logging(logs_dir=args.logs_dir, log_level=args.logging_level)
    logger.info("Starting PublicationProfiler")

    async def main():        
        # Load data
        if args.local_data_path is not None:
            logger.info(f"Loading data from local file: {args.local_data_path}")
            if args.local_data_path.endswith(".xlsx"):
                data = pd.read_excel(args.local_data_path)
            elif args.local_data_path.endswith(".csv"):
                data = pd.read_csv(args.local_data_path)
            elif args.local_data_path.endswith(".json"):
                data = pd.read_json(args.local_data_path)
            else:
                raise ValueError(f"Unsupported file type: {args.local_data_path}")
        else:
            logger.info(f"Fetching data from OpenAlex database: {args.openalex_db_path}")
            data = fetch_publication_data_from_db(logger, args.openalex_db_path)

        if len(data) == 0:
            logger.error("No data found to process!")
            return

        logger.info(f"Total publications to process: {len(data)}")

        # load llm config
        logger.info("Loading LLM configuration")
        llm_config = json.load(open("config/llm_config.json"))["publication_profiler"]

        # Initialize the agent with logger and database
        logger.info("Initializing PublicationProfilerAgent")
        agent = PublicationProfilerAgent(
            logger=logger, 
            db_path=args.db_path, 
            model_params=llm_config
        )
        
        # Analyze publications
        logger.info(f"Starting analysis with max concurrent tasks: {args.max_concurrent}")
        await agent.analyze_dataframe(data, max_concurrent=args.max_concurrent)
        logger.info("Analysis completed successfully")
    
    # Run the example
    asyncio.run(main())
