import argparse
import pandas as pd
import asyncio
import os
import sqlite3
import logging
import json
import traceback
import re
from typing import List, Dict, Optional, Literal
from openai import AsyncAzureOpenAI
from pydantic_ai import Agent, Tool, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from tqdm import tqdm
from dotenv import load_dotenv
from datetime import datetime
from pathlib import Path
from jinja2 import Template
from utils.extraction_pydantic_models import (
    AntibodyDrugConjugate, 
    Model, 
    Endpoints, 
    ExtractionState, 
    ExperimentResult, 
    ModelType, 
    CitedValue,
    CitedEnumValue
)
from langgraph.graph import StateGraph
from pydantic import BaseModel, Field
import sys


class ExtractionPipeline:
    def __init__(
        self,
        logs_dir: str = "logs",
        logging_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"
    ):
        """Initialize the extraction pipeline with logging configuration.
        
        Args:
            logs_dir: Directory where log files will be stored
            logging_level: Logging level for the pipeline
        """
        self.logger = self._setup_logging(logs_dir, logging_level)
        self._initialize_llm()
        self._initialize_agents()
        
    def _setup_logging(self, logs_dir: str, logging_level: str) -> logging.Logger:
        """Configure logging with both file and console handlers."""
        # Create logs directory if it doesn't exist
        log_path = Path(logs_dir)
        log_path.mkdir(exist_ok=True)
        
        # Create a timestamped log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_path / f"extraction_pipeline_{timestamp}.log"
        
        # Set log level
        log_level = getattr(logging, logging_level.upper())
        
        # Create formatters
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Setup handlers
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(console_formatter)
        
        # Use UTF-8 encoding for file handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        
        # Configure logger
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.DEBUG)
        logger.handlers = []  # Clear any existing handlers
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        logger.propagate = False
        
        logger.info(f"Logging initialized. Log file: {log_file}")
        return logger

    def _initialize_llm(self):
        """Initialize Azure OpenAI client and model."""
        try:
            load_dotenv()
            
            self.client = AsyncAzureOpenAI(
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                azure_deployment='gpt-4o',
                api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            )
            
            self.llm = OpenAIModel(
                model_name='gpt-4o',
                provider=OpenAIProvider(openai_client=self.client)
            )
            
            self.logger.info("LLM initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

    def _initialize_agents(self):
        """Initialize extraction agents without tools."""
        try:
            # Initialize agents with debug logging for prompts
            adc_system_prompt = open("prompts/adc_extraction_system_prompt.md").read()
            self.logger.debug(f"ADC System Prompt: {adc_system_prompt}")
            
            self.adc_agent = Agent(
                model=self.llm,
                system_prompt=adc_system_prompt,
                result_type=List[AntibodyDrugConjugate],
                retries=3,
                result_retries=3
                
            )
            
            self.model_agent = Agent(
                model=self.llm,
                system_prompt=open("prompts/model_extraction_system_prompt.md").read(),
                result_type=Model,
                retries=3,
                result_retries=3
                
            )
            
            self.endpoint_agent = Agent(
                model=self.llm,
                system_prompt=open("prompts/endpoint_extraction_system_prompt.md").read(),
                result_type=Endpoints,
                retries=3,
                result_retries=3
                
            )
            
            self.logger.info("Agents initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agents: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

    @staticmethod
    def calculate_entity_indices(full_text: str, text_snippet: str, entity: str) -> tuple[int, int]:
        """Calculate the start and end indices of an entity in the full text."""
        try:
            logger = logging.getLogger(__name__)

            def normalize_text(text: str) -> str:
                """Normalize text by:
                1. Converting newlines to spaces
                2. Removing trailing periods
                3. Normalizing spaces
                """
                # Convert newlines to spaces first
                text = re.sub(r'\n+', ' ', text)
                
                # Remove trailing period
                text = re.sub(r'\.$', '', text)
                
                # Normalize multiple spaces to single space
                text = re.sub(r'\s+', ' ', text)
                
                return text.strip()

            # Create a mapping of normalized positions to original positions
            original_positions = []
            
            for i, _ in enumerate(full_text):
                
                original_positions.append(i)

            # Normalize all texts
            normalized_snippet = normalize_text(text_snippet)
            normalized_entity = normalize_text(entity)
            normalized_text = normalize_text(full_text)
            
            logger.debug(f"Normalized snippet: '{normalized_snippet}'")
            logger.debug(f"Normalized text excerpt: '{normalized_text[:100]}'")
            
            # Find the snippet in normalized text
            snippet_start = normalized_text.lower().find(normalized_snippet.lower())
            if snippet_start == -1:
                return 0, 0
                
            # Find the entity within the snippet
            entity_in_snippet = normalized_snippet.lower().find(normalized_entity.lower())
            if entity_in_snippet == -1:
                return 0, 0
            
            # Calculate the actual positions using the mapping
            entity_start_normalized = snippet_start + entity_in_snippet
            entity_end_normalized = entity_start_normalized + len(normalized_entity)
            
            # Map back to original positions
            entity_start = original_positions[entity_start_normalized]
            entity_end = original_positions[entity_end_normalized - 1] + 1
            
            return entity_start, entity_end
        
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Error calculating indices: {str(e)}")
            return 0, 0

    def extract_adcs(self, state: ExtractionState) -> ExtractionState:
        """Extract ADCs from the text."""
        try:
            self.logger.info("Starting ADC extraction")
            
            # Render user prompt
            user_prompt = Template(open("prompts/adc_extraction_user_prompt.md").read()).render(
                TEXT=state.raw_text
            )
            
            try:
                # Run extraction with context
                result = self.adc_agent.run_sync(user_prompt)
            except Exception as agent_error:
                # Log error handling (unchanged)
                raise agent_error
            
            # Ensure adcs is always a list
            adcs = [result.data] if isinstance(result.data, AntibodyDrugConjugate) else result.data
            
            self.logger.debug(f"Extracted ADCs: {adcs}")
            
            # Update indices for each ADC using recursive function
            for adc in adcs:
                self._update_model_indices(adc, state.raw_text)
            
            # Create experiment results with updated ADCs
            state.experiment_results = [
                ExperimentResult(
                    adc=adc,
                    model=[],
                    endpoints=[]
                )
                for adc in adcs
            ]
            
            self.logger.info(f"Successfully extracted {len(adcs)} ADCs")
            return state
        
        except Exception as e:
            self.logger.error(f"Error during ADC extraction: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

    def _update_indices(self, cited_value, raw_text):
        """Helper method to update indices for both CitedValue and CitedEnumValue objects"""
        if cited_value and cited_value.citation:
            # Get the value string - handle both regular values and enum values
            if hasattr(cited_value.value, 'value'):  # It's an enum
                value_str = str(cited_value.value.value)
            else:  # It's a regular value
                value_str = str(cited_value.value)
            
            start_ind, end_ind = self.calculate_entity_indices(
                raw_text, 
                cited_value.citation, 
                value_str
            )
            cited_value.start_ind = start_ind
            cited_value.end_ind = end_ind

    def _update_model_indices(self, model_obj, raw_text):
        """Recursively update indices for all CitedValue objects in a model"""
        
        # Base case: if it's a CitedValue or CitedEnumValue, update its indices
        if hasattr(model_obj, 'citation') and hasattr(model_obj, 'value'):
            self._update_indices(model_obj, raw_text)
            return
        
        # If it's a Pydantic model, process each field
        if isinstance(model_obj, BaseModel):
            for field_name, field_value in model_obj:
                if field_value is not None:  # Skip None values
                    self._update_model_indices(field_value, raw_text)
        
        # If it's a list or similar iterable, process each item
        elif isinstance(model_obj, (list, tuple)):
            for item in model_obj:
                if item is not None:
                    self._update_model_indices(item, raw_text)
        
        # If it's a dict, process each value
        elif isinstance(model_obj, dict):
            for value in model_obj.values():
                if value is not None:
                    self._update_model_indices(value, raw_text)

    def extract_models(self, state: ExtractionState) -> ExtractionState:
        """Extract models for each ADC."""
        try:
            self.logger.info("Starting model extraction")
            
            for exp_result in state.experiment_results:    
                user_prompt = Template(open("prompts/model_extraction_user_prompt.md").read()).render(
                    TEXT=state.raw_text,
                    ADC=exp_result.adc.model_dump_json()
                )
                try:
                    result = self.model_agent.run_sync(user_prompt, deps=state.raw_text)
                    model = result.data
                    
                    # Update all indices recursively
                    self._update_model_indices(model, state.raw_text)
                    
                    exp_result.model = model
                    
                except Exception as agent_error:
                    # Error handling (unchanged)
                    if hasattr(agent_error, 'raw_response'):
                        self.logger.error("LLM Raw Response:")
                        self.logger.error(json.dumps(agent_error.raw_response, indent=2))
                    
                    self.logger.error("Expected Schema (Model):")
                    model_schema = Model.model_json_schema()
                    self.logger.error(json.dumps(model_schema, indent=2))
                    raise
                    
            self.logger.info(f"Successfully extracted models for {len(state.experiment_results)} ADCs")
            return state
        
        except Exception as e:
            self.logger.error(f"Error during model extraction: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

    def extract_endpoints(self, state: ExtractionState) -> ExtractionState:
        """Extract endpoints for each ADC and its model."""
        try:
            self.logger.info("Starting endpoint extraction")
            
            for exp_result in state.experiment_results:
                user_prompt = Template(open("prompts/endpoint_extraction_user_prompt.md").read()).render(
                    TEXT=state.raw_text,
                    ADC=exp_result.adc.model_dump_json(),
                    MODEL=exp_result.model.model_dump_json()
                )
                
                try:
                    result = self.endpoint_agent.run_sync(user_prompt, deps=state.raw_text)
                    endpoints = result.data.endpoints
                    
                    # Update all indices recursively
                    self._update_model_indices(endpoints, state.raw_text)
                    
                    exp_result.endpoints = endpoints
                    
                except Exception as agent_error:
                    # Error handling (unchanged)
                    if hasattr(agent_error, 'raw_response'):
                        self.logger.error("LLM Raw Response:")
                        self.logger.error(json.dumps(agent_error.raw_response, indent=2))
                    
                    self.logger.error("Expected Schema (Endpoints):")
                    endpoints_schema = Endpoints.model_json_schema()
                    self.logger.error(json.dumps(endpoints_schema, indent=2))
                    raise
                
            self.logger.info(f"Successfully extracted endpoints for {len(state.experiment_results)} ADCs")
            return state
            
        except Exception as e:
            self.logger.error(f"Error during endpoint extraction: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

    def build_pipeline(self, adcs_only: bool = False) -> StateGraph:
        """Build the extraction pipeline."""
        try:
            self.logger.info("Building extraction pipeline")
            graph = StateGraph(ExtractionState)
            
            # Add nodes
            graph.add_node("extract_adcs", self.extract_adcs)
            
            if not adcs_only:
                graph.add_node("extract_models", self.extract_models)
                graph.add_node("extract_endpoints", self.extract_endpoints)
                
                # Add edges
                graph.add_edge("extract_adcs", "extract_models")
                graph.add_edge("extract_models", "extract_endpoints")
            
            # Set entry point
            graph.set_entry_point("extract_adcs")
            
            self.logger.info(f"Pipeline built successfully (adcs_only={adcs_only})")
            return graph.compile()
            
        except Exception as e:
            self.logger.error(f"Error building pipeline: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

    def save_results(self, state: ExtractionState, output_path: Path, adcs_only: bool = False):
        """Save extraction results to JSON file."""
        try:
            self.logger.info(f"Saving results to {output_path}")
            
            results = {
                'experiment_results': [
                    {
                        'adc': exp_result.adc.model_dump(),
                           
                        'model': exp_result.model.model_dump() if exp_result.model and not adcs_only else None,
                        'endpoints': [endpoint.model_dump() for endpoint in exp_result.endpoints] if not adcs_only and exp_result.endpoints else []
                    }
                    for exp_result in state['experiment_results']
                ]
            }
            
            # Ensure the output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save to JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
                
            self.logger.info("Results saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {str(e)}")
            self.logger.debug(f"Error details: {traceback.format_exc()}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Extract information from scientific papers")
    parser.add_argument("--input-dir", type=str, required=True, help="Directory containing input files")
    parser.add_argument("--output-dir", type=str, required=True, help="Directory for output files")
    parser.add_argument("--logs-dir", type=str, default="logs", help="Directory for log files")
    parser.add_argument("--logging-level", type=str, default="INFO", 
                       choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                       help="Set logging level")
    parser.add_argument("--adcs-only", action="store_true", help="Extract only ADCs without models and endpoints")
    parser.add_argument("--force-reprocess", action="store_true", help="Force reprocessing of already processed files")
    args = parser.parse_args()

    try:
        # Initialize pipeline
        pipeline = ExtractionPipeline(logs_dir=args.logs_dir, logging_level=args.logging_level)
        pipeline.logger.info("Starting extraction pipeline")
        
        # Process each file
        input_path = Path(args.input_dir)
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Get list of files to process
        input_files = list(input_path.glob("*.md"))
        total_files = len(input_files)
        
        with tqdm(total=total_files, desc="Processing files") as pbar:
            for input_file in input_files:
                try:
                    pbar.set_description(f"Processing {input_file.name}")
                    
                    # Check if file should be processed
                    output_file = output_path / f"{input_file.stem}_results.json"
                    if output_file.exists() and not args.force_reprocess:
                        pipeline.logger.info(f"Skipping {input_file} as it has already been processed")
                        pbar.update(1)
                        continue

                    # Read input file
                    with open(input_file, 'r', encoding='utf-8') as f:
                        text = f.read()
                    
                    # Initialize state
                    initial_state = ExtractionState(raw_text=text)
                    
                    # Build and run pipeline
                    graph = pipeline.build_pipeline(adcs_only=args.adcs_only)
                    final_state = graph.invoke(initial_state)
                    
                    # Save results
                    pipeline.save_results(final_state, output_file, adcs_only=args.adcs_only)
                    
                    pbar.update(1)
                    
                except Exception as e:
                    pipeline.logger.error(f"Error processing file {input_file}: {str(e)}")
                    pipeline.logger.debug(f"Error details: {traceback.format_exc()}")
                    pbar.update(1)
                    continue
        
        pipeline.logger.info("Extraction pipeline completed")
        
    except Exception as e:
        logging.error(f"Fatal error in extraction pipeline: {str(e)}")
        logging.debug(f"Error details: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
