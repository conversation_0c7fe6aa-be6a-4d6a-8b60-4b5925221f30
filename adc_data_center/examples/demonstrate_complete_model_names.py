"""
Demonstration script showing the complete model name preservation functionality.

This script shows how the updated model extraction pipeline now preserves complete model names
with full experimental context instead of using simplified identifiers.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from src.utils.extraction_pydantic_models import ExperimentalModel
from jinja2 import Template


def demonstrate_complete_model_names():
    """Demonstrate the complete model name preservation functionality."""
    
    print("=" * 80)
    print("DEMONSTRATION: Complete Model Name Preservation")
    print("=" * 80)
    print()
    
    print("PROBLEM SOLVED:")
    print("Previously, the model extraction pipeline was instructed to use simplified model")
    print("identifiers (e.g., 'SK-BR-3', 'HER2 protein') instead of complete experimental")
    print("context. This led to loss of crucial experimental details.")
    print()
    
    # Show the before/after comparison
    print("BEFORE vs AFTER COMPARISON:")
    print("-" * 50)
    
    before_after_examples = [
        {
            "category": "Cell Line Models",
            "before": "SK-BR-3",
            "after": "SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
            "context_gained": ["human", "breast adenocarcinoma", "cell line", "HER2-positive"]
        },
        {
            "category": "Protein-based Assays",
            "before": "HER2 protein",
            "after": "His-tagged HER2-ECD protein (used in ELISA binding assay)",
            "context_gained": ["His-tagged", "ECD", "ELISA", "binding assay"]
        },
        {
            "category": "Xenograft Models",
            "before": "NCI-N87",
            "after": "NCI-N87 xenograft model (HER2-positive, subcutaneous in female nude mice)",
            "context_gained": ["xenograft model", "HER2-positive", "subcutaneous", "female nude mice"]
        },
        {
            "category": "PDX Models",
            "before": "NIBIO G016",
            "after": "NIBIO G016 gastric cancer patient-derived xenograft (PDX) model (HER2 IHC 3+/FISH+)",
            "context_gained": ["gastric cancer", "patient-derived xenograft", "PDX", "HER2 IHC 3+", "FISH+"]
        },
        {
            "category": "Animal Models",
            "before": "Cynomolgus monkeys",
            "after": "Cynomolgus monkeys (cross-reactive species for DS-8201a, used for pharmacokinetics and toxicity studies)",
            "context_gained": ["cross-reactive species", "DS-8201a", "pharmacokinetics", "toxicity studies"]
        }
    ]
    
    for example in before_after_examples:
        print(f"{example['category']}:")
        print(f"  ❌ Before: {example['before']}")
        print(f"  ✅ After:  {example['after']}")
        print(f"  📈 Context Gained: {', '.join(example['context_gained'])}")
        print()
    
    # Demonstrate the ExperimentalModel creation with complete names
    print("EXPERIMENTAL MODEL CREATION WITH COMPLETE NAMES:")
    print("-" * 50)
    
    models = [
        ExperimentalModel(
            citations=[
                "SK-BR-3 cells were preincubated in a 96-well plate for 4 days",
                "DS-8201a showed ADCC activity with an EC50 of 3.8 ng/mL in SK-BR-3 cells"
            ],
            model_name="SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
            clinical_human_related=False,
            cancer_type="Breast Cancer",
            cancer_subtype="HER2-positive breast cancer",
            investigative=True,
            reasoning_for_inclusion="SK-BR-3 was selected for cellular assays including ADCC activity and cytotoxicity measurements due to its high HER2 expression levels, making it ideal for evaluating HER2-targeting ADC cellular responses."
        ),
        ExperimentalModel(
            citations=[
                "Kd values of DS-8201a were determined by ELISA using HER2 ECD protein",
                "immunoplates were coated with 2.5 mg/mL His-tagged HER2-ECD protein"
            ],
            model_name="His-tagged HER2-ECD protein (used in ELISA binding assay)",
            clinical_human_related=False,
            cancer_type="Protein-based binding assay",
            investigative=True,
            reasoning_for_inclusion="His-tagged HER2-ECD protein was used for direct binding affinity measurements (Kd) in ELISA format to assess ADC binding to HER2 target without cellular interference, providing accurate binding kinetics data."
        ),
        ExperimentalModel(
            citations=[
                "DS-8201a induced tumor growth inhibition in NCI-N87 xenograft model",
                "NCI-N87 cells were subcutaneously implanted in female nude mice"
            ],
            model_name="NCI-N87 xenograft model (HER2-positive, subcutaneous in female nude mice)",
            clinical_human_related=False,
            cancer_type="Gastric Cancer",
            cancer_subtype="HER2-positive gastric cancer",
            investigative=True,
            reasoning_for_inclusion="NCI-N87 xenograft model was selected for in vivo efficacy studies due to its HER2-positive status and established tumor growth characteristics, providing a relevant model for evaluating ADC antitumor activity in a gastric cancer context."
        )
    ]
    
    for i, model in enumerate(models, 1):
        print(f"{i}. Model: {model.model_name}")
        print(f"   Cancer Type: {model.cancer_type}")
        if model.cancer_subtype:
            print(f"   Cancer Subtype: {model.cancer_subtype}")
        print(f"   Reasoning: {model.reasoning_for_inclusion[:100]}...")
        print()
    
    # Show enhanced context creation with complete names
    print("ENHANCED MODEL CONTEXT WITH COMPLETE NAMES:")
    print("-" * 50)
    
    model_contexts = []
    model_names = []
    
    for model in models:
        model_names.append(model.model_name)
        
        # Build comprehensive model context (same logic as in extract_endpoints)
        context_parts = [f"Model Name: {model.model_name}"]
        
        if model.cancer_type and model.cancer_type != "NONE":
            context_parts.append(f"Cancer Type: {model.cancer_type}")
            
        if model.cancer_subtype:
            context_parts.append(f"Cancer Subtype: {model.cancer_subtype}")
            
        if model.reasoning_for_inclusion:
            context_parts.append(f"Model Selection Rationale: {model.reasoning_for_inclusion}")
            
        if model.citations:
            key_citations = model.citations[:2]
            context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
        
        model_contexts.append(" | ".join(context_parts))
    
    print("Enhanced contexts with complete model names:")
    for i, context in enumerate(model_contexts, 1):
        print(f"{i}. {context[:150]}...")
        print()
    
    # Show prompt template compatibility
    print("PROMPT TEMPLATE COMPATIBILITY:")
    print("-" * 50)
    
    # Sample endpoint extraction prompt template
    sample_prompt = """
ADC Name: DS-8201a

The models to analyse:
{{MODELS}}

Detailed Model Context and Characteristics:
{% for context in MODEL_CONTEXTS %}
- {{context}}
{% endfor %}

ENHANCED CONTEXT AWARENESS: Use the detailed model context provided above to better understand:
- Specific experimental systems (cell lines vs protein assays vs xenografts)
- Model characteristics that affect endpoint interpretation
- Experimental context for proper endpoint association
"""
    
    template = Template(sample_prompt)
    rendered = template.render(
        MODELS=model_names,
        MODEL_CONTEXTS=model_contexts
    )
    
    print("Sample rendered prompt with complete model names:")
    print(rendered[:800] + "...")
    print()
    
    # Show the benefits
    print("BENEFITS OF COMPLETE MODEL NAMES:")
    print("-" * 50)
    
    benefits = [
        "🎯 PRECISE IDENTIFICATION: Each model is uniquely identified with full experimental context",
        "🧬 EXPERIMENTAL CLARITY: Cell lines, proteins, xenografts are clearly distinguished",
        "🔬 ASSAY CONTEXT: Binding assays, cytotoxicity assays, in vivo studies are properly identified",
        "📊 BETTER ASSOCIATIONS: Endpoints can be correctly associated with appropriate experimental systems",
        "🏥 CLINICAL RELEVANCE: Model characteristics relevant to clinical translation are preserved",
        "🔍 ENHANCED INTERPRETATION: Full context enables better understanding of experimental results",
        "📝 COMPREHENSIVE DOCUMENTATION: Complete experimental details are maintained throughout pipeline",
        "⚖️ SCIENTIFIC ACCURACY: Reflects actual experimental methodology described in papers"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    print()
    
    # Show specific improvements for endpoint association
    print("IMPROVED ENDPOINT ASSOCIATIONS:")
    print("-" * 50)
    
    associations = [
        {
            "endpoint": "ADC_KD",
            "correct_model": "His-tagged HER2-ECD protein (used in ELISA binding assay)",
            "incorrect_model": "SK-BR-3",
            "explanation": "Binding affinity measurements require protein-based assays, not cell lines"
        },
        {
            "endpoint": "ADC_IC50",
            "correct_model": "SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
            "incorrect_model": "HER2 protein",
            "explanation": "Cytotoxicity measurements require viable cells, not purified proteins"
        },
        {
            "endpoint": "ANTI_TUMOR_ACTIVITY_DOSE",
            "correct_model": "NCI-N87 xenograft model (HER2-positive, subcutaneous in female nude mice)",
            "incorrect_model": "NCI-N87",
            "explanation": "In vivo efficacy requires animal models, not just cell line names"
        }
    ]
    
    for assoc in associations:
        print(f"Endpoint: {assoc['endpoint']}")
        print(f"  ✅ Correct Model: {assoc['correct_model']}")
        print(f"  ❌ Incorrect Model: {assoc['incorrect_model']}")
        print(f"  💡 Why: {assoc['explanation']}")
        print()
    
    print("=" * 80)
    print("IMPLEMENTATION CHANGES MADE:")
    print("=" * 80)
    print()
    
    changes = [
        "✅ Updated model_extraction_user_prompt.md to preserve complete model names",
        "✅ Removed instructions that told agents to 'trim' or 'simplify' model names",
        "✅ Added emphasis on including ALL experimental context and details",
        "✅ Enhanced model_extraction_system_prompt.md to emphasize complete naming",
        "✅ Maintained compatibility with enhanced model context functionality",
        "✅ Created comprehensive tests to verify complete name preservation",
        "✅ Documented the changes and benefits in detail"
    ]
    
    for change in changes:
        print(f"  {change}")
    print()
    
    print("The complete model name preservation ensures that the extraction pipeline")
    print("maintains full experimental context throughout the process, enabling more")
    print("accurate model-endpoint associations and better scientific interpretation.")
    print()
    print("=" * 80)
    print("Demonstration complete!")
    print("=" * 80)


if __name__ == "__main__":
    demonstrate_complete_model_names()
