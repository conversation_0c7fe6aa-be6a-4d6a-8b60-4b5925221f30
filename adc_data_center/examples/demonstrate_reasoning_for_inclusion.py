"""
Demonstration script showing the new reasoning_for_inclusion field in ExperimentalModel.

This script shows how the reasoning_for_inclusion field captures and stores the reasoning 
behind why each particular model was extracted during the extraction pipeline.
"""

import json
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from src.utils.extraction_pydantic_models import ExperimentalModel


def demonstrate_reasoning_for_inclusion():
    """Demonstrate the new reasoning_for_inclusion functionality."""
    
    print("=" * 80)
    print("DEMONSTRATION: reasoning_for_inclusion field in ExperimentalModel")
    print("=" * 80)
    print()
    
    # Example 1: Cell line model with detailed reasoning
    print("Example 1: HER2-positive breast cancer cell line")
    print("-" * 50)
    
    model1_data = {
        "citations": [
            "The ADC was evaluated in SK-BR-3 cells, a HER2-positive breast cancer cell line.",
            "SK-BR-3 cells express high levels of HER2 antigen (3+ by IHC).",
            "This cell line is widely used for HER2-targeting therapeutic evaluation."
        ],
        "model_name": "SK-BR-3",
        "clinical_human_related": False,
        "cancer_type": "Breast Cancer",
        "cancer_subtype": "HER2-positive breast cancer",
        "investigative": True,
        "reasoning_for_inclusion": "SK-BR-3 was selected as the primary in vitro model due to its high HER2 expression levels (3+ by IHC), making it an ideal system for evaluating HER2-targeting ADC binding affinity and cytotoxic efficacy. This well-characterized cell line provides a reliable platform for dose-response studies and represents a clinically relevant breast cancer subtype that would benefit from HER2-targeted therapy."
    }
    
    model1 = ExperimentalModel(**model1_data)
    print(f"Model Name: {model1.model_name}")
    print(f"Cancer Type: {model1.cancer_type}")
    print(f"Cancer Subtype: {model1.cancer_subtype}")
    print(f"Investigative: {model1.investigative}")
    print()
    print("Reasoning for Inclusion:")
    print(f"  {model1.reasoning_for_inclusion}")
    print()
    
    # Example 2: Xenograft model with different reasoning
    print("Example 2: Patient-derived xenograft model")
    print("-" * 50)
    
    model2_data = {
        "citations": [
            "The ADC was tested in a PDX model derived from a triple-negative breast cancer patient.",
            "This PDX model retains the heterogeneity and drug resistance patterns of the original tumor.",
            "The model was selected for its clinical relevance and predictive value."
        ],
        "model_name": "TNBC-PDX-001",
        "clinical_human_related": False,
        "cancer_type": "Breast Cancer",
        "cancer_subtype": "Triple-negative breast cancer",
        "investigative": True,
        "reasoning_for_inclusion": "TNBC-PDX-001 was chosen for its superior clinical translatability compared to cell line models. This patient-derived xenograft maintains the tumor microenvironment, genetic heterogeneity, and drug resistance mechanisms of the original triple-negative breast cancer, providing a more accurate prediction of clinical efficacy. The model is particularly valuable for evaluating ADC penetration and distribution in complex tumor architecture."
    }
    
    model2 = ExperimentalModel(**model2_data)
    print(f"Model Name: {model2.model_name}")
    print(f"Cancer Type: {model2.cancer_type}")
    print(f"Cancer Subtype: {model2.cancer_subtype}")
    print(f"Investigative: {model2.investigative}")
    print()
    print("Reasoning for Inclusion:")
    print(f"  {model2.reasoning_for_inclusion}")
    print()
    
    # Example 3: Safety/toxicology model
    print("Example 3: Non-human primate toxicology model")
    print("-" * 50)
    
    model3_data = {
        "citations": [
            "Toxicology studies were conducted in cynomolgus monkeys.",
            "This species was selected due to cross-reactivity with the target antigen.",
            "The model allows evaluation of systemic toxicity and pharmacokinetics."
        ],
        "model_name": "Cynomolgus monkey",
        "clinical_human_related": False,
        "cancer_type": "Non-cancer model",
        "investigative": True,
        "reasoning_for_inclusion": "Cynomolgus monkeys were selected for toxicology assessment based on their demonstrated cross-reactivity with the target antigen, similar tissue distribution patterns to humans, and established use in ADC safety evaluation. This model enables comprehensive assessment of dose-limiting toxicities, target-mediated adverse effects, and pharmacokinetic parameters that are critical for determining safe starting doses in clinical trials."
    }
    
    model3 = ExperimentalModel(**model3_data)
    print(f"Model Name: {model3.model_name}")
    print(f"Cancer Type: {model3.cancer_type}")
    print(f"Investigative: {model3.investigative}")
    print()
    print("Reasoning for Inclusion:")
    print(f"  {model3.reasoning_for_inclusion}")
    print()
    
    # Show JSON serialization
    print("JSON Serialization Example:")
    print("-" * 50)
    
    models_list = [model1, model2, model3]
    serialized_models = [model.model_dump() for model in models_list]
    
    print("Sample JSON output (first model):")
    print(json.dumps(serialized_models[0], indent=2))
    print()
    
    # Show how this integrates with the extraction pipeline
    print("Integration with Extraction Pipeline:")
    print("-" * 50)
    print("1. The model extraction agent now requests reasoning for each model selection")
    print("2. The reasoning is captured in the 'reasoning_for_inclusion' field")
    print("3. This provides transparency and traceability for model selection decisions")
    print("4. The reasoning is saved in JSON output and included in Excel reports")
    print("5. Similar to how 'reasoning_for_inclusion' works for endpoint measurements")
    print()
    
    print("Benefits:")
    print("- Provides transparency in model selection criteria")
    print("- Helps validate the appropriateness of chosen models")
    print("- Enables better understanding of experimental design rationale")
    print("- Facilitates quality control and review processes")
    print("- Supports regulatory submissions with clear justifications")
    print()
    
    print("=" * 80)
    print("Demonstration complete!")
    print("=" * 80)


if __name__ == "__main__":
    demonstrate_reasoning_for_inclusion()
