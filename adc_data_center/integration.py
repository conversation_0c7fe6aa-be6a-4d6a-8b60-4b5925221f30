import subprocess
import sys
import os
import argparse
import logging
from pathlib import Path
from typing import List, Optional
 
# Configure logging
def setup_logging(log_file: str, level=logging.INFO):
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    logging.basicConfig(
        filename=log_file,
        level=level,
        format="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    console = logging.StreamHandler()
    console.setLevel(level)
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
    console.setFormatter(formatter)
    logging.getLogger("").addHandler(console)
 
 
def run_subprocess(cmd: List[str], cwd: Optional[str] = None):
    """Run a subprocess command with real-time logging."""
    logging.info("Executing command: %s", " ".join(cmd))
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=cwd
        )
        for line in process.stdout:
            logging.info(line.strip())
        process.wait()
        if process.returncode != 0:
            logging.error("Command failed with exit code %d", process.returncode)
            sys.exit(process.returncode)
    except Exception as e:
        logging.exception("Subprocess execution failed: %s", e)
        sys.exit(1)
 
 
def run_extraction(input_dir: str, output_dir: str, logs_dir: str):
    cmd = [
        sys.executable,
        "src/extraction_pipeline.py",
        "--input-dir", input_dir,
        "--output-dir", output_dir,
        "--logs-dir", logs_dir,
        "--logging-level", "DEBUG"
    ]
    run_subprocess(cmd)
 
 
def extract_priority_mapping(csv_path: str):
    cmd = [
        sys.executable,
        "-m", "regression_evaluation.src.transform.priority_mapping_extractor",
        "--csv-path", csv_path
    ]
    run_subprocess(cmd)
 
 
def convert_gt_to_json(csv_path: str, output_dir: str):
    cmd = [
        sys.executable,
        "-m", "regression_evaluation.src.transform.unified_ground_truth_cli",
        "--csv-path", csv_path,
        "--output-dir", output_dir
    ]
    run_subprocess(cmd)
 
 
def verify_standardization(gt_dir: str, extraction_dir: str, output_dir: str):
    cmd = [
        sys.executable,
        "-m", "regression_evaluation.src.transform.verify_standardization",
        "--gt-dir", gt_dir,
        "--result-dir", extraction_dir
    ]
    run_subprocess(cmd)
 
 
def run_regression_evaluation(
    data_dir: str,
    output_dir: str,
    logs_dir: str,
    paper_mode: str = "all",
    paper_ids: Optional[List[str]] = None
):
    cmd = [
        sys.executable,
        "-m", "regression_evaluation.src.main",
        "--data-dir", data_dir,
        "--output-path", output_dir,
        "--log-file", os.path.join(logs_dir, "regression_eval.log")
    ]
    if paper_mode == "all":
        cmd.append("--all-papers")
    elif paper_mode == "single" and paper_ids and len(paper_ids) == 1:
        cmd.extend(["--paper-id", paper_ids[0]])
    elif paper_mode == "multiple" and paper_ids:
        cmd.append("--paper-ids")
        cmd.extend(paper_ids)
    else:
        logging.error("Invalid paper_mode or paper_ids configuration")
        sys.exit(1)
 
    run_subprocess(cmd)
 
 
def convert_evaluation_to_csv(input_dir: str, output_dir: str):
    cmd = [
        sys.executable,
        "regression_evaluation/src/transform/convert_evaluation_json_to_csv.py",
        "--input-dir", input_dir,
        "--output-dir", output_dir
    ]
    run_subprocess(cmd)
 
 
def generate_evaluation_summary(input_dir: str, output_dir: str):
    cmd = [
        sys.executable,
        "regression_evaluation/src/transform/evaluation_precision_summary.py",
        "--input-dir", input_dir,
        "--output-dir", output_dir
    ]
    run_subprocess(cmd)
 
 
def run_pipeline(
    input_dir: str,
    csv_path: str,
    extraction_output: str,
    gt_output: str,
    corrected_output: str,
    evaluation_output: str,
    logs_dir: str,
    paper_mode: str = "all",
    paper_ids: Optional[List[str]] = None
):
    setup_logging(os.path.join(logs_dir, "pipeline.log"))
 
    logging.info("Starting the regression evaluation pipeline")
    
    run_extraction(input_dir, extraction_output, logs_dir)
    extract_priority_mapping(csv_path)
    convert_gt_to_json(csv_path, gt_output)
    verify_standardization(gt_output, extraction_output, corrected_output)
    run_regression_evaluation(
        data_dir=corrected_output,
        output_dir=evaluation_output,
        logs_dir=logs_dir,
        paper_mode=paper_mode,
        paper_ids=paper_ids
    )
    convert_evaluation_to_csv(evaluation_output, os.path.join(evaluation_output, "CSV"))
    generate_evaluation_summary(
        input_dir=os.path.join(evaluation_output),
        output_dir=os.path.join(evaluation_output, "Summary")
    )
    logging.info("Pipeline completed successfully.")
 
 
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run full regression evaluation pipeline.")
    parser.add_argument("--input-dir", required=True, help="Input directory containing markdown files for extraction")
    parser.add_argument("--csv-path", required=True, help="Ground Truth CSV path")
    parser.add_argument("--extraction-output", default="regression_evaluation/data/extraction_results", help="Extraction output directory")
    parser.add_argument("--gt-output", default="regression_evaluation/data/ground-truth", help="Ground truth JSON output directory")
    parser.add_argument("--corrected-output", default="regression_evaluation/data", help="Corrected results output directory")
    parser.add_argument("--evaluation-output", default="regression_evaluation/data/evaluations", help="Evaluation output directory")
    parser.add_argument("--logs-dir", default="logs", help="Directory for logs")
    parser.add_argument("--paper-mode", choices=["all", "single", "multiple"], default="all", help="Paper selection mode")
    parser.add_argument("--paper-ids", nargs="+", help="Paper IDs for single or multiple mode")
    
    args = parser.parse_args()
    
    run_pipeline(
        input_dir=args.input_dir,
        csv_path=args.csv_path,
        extraction_output=args.extraction_output,
        gt_output=args.gt_output,
        corrected_output=args.corrected_output,
        evaluation_output=args.evaluation_output,
        logs_dir=args.logs_dir,
        paper_mode=args.paper_mode,
        paper_ids=args.paper_ids
    )
