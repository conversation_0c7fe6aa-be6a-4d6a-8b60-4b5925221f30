#!/usr/bin/env python3
"""
Test script to run ground truth conversion directly.
"""

import sys
import logging
from pathlib import Path

# Add current directory to path for proper imports
sys.path.insert(0, '.')

from src.transform.unified_ground_truth_converter import UnifiedGroundTruthConverter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    csv_path = "data/AZ_ADC_ScaleUp_Groundtruth_curation_master(Endpoints) (5).csv"
    output_dir = "data/ground-truth/"
    
    try:
        # Initialize converter
        logger.info("Initializing ground truth converter")
        converter = UnifiedGroundTruthConverter(csv_path=csv_path)
        
        # Load CSV file
        logger.info("Loading CSV file...")
        converter.load_csv_file()
        
        # Load priority mapping
        logger.info("Loading priority mapping...")
        converter.load_priority_mapping()
        
        # Get available paper IDs
        available_papers = converter.get_unique_paper_ids()
        logger.info(f"Found {len(available_papers)} papers: {available_papers[:5]}...")
        
        # Save results
        logger.info("Starting conversion...")
        stats = converter.save_ground_truth_files(
            output_dir=output_dir,
            validate_only=False
        )
        
        # Report results
        logger.info(f"""
Conversion complete!
- Processed: {stats['processed']} papers
- Skipped: {stats['skipped']} papers  
- Errors: {stats['errors']} papers
- Output: {output_dir}
        """)
        
        if stats['errors'] > 0:
            logger.warning("Some papers had errors during processing.")
            return 1
        
        return 0
        
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
