"""
Results writer implementations for the ADC evaluation system.

This module provides implementations of the ResultsWriter interface for
saving evaluation results in various formats.

TEST FILES: tests/test_main.py, tests/test_refactored_code.py, tests/test_gt_removal.py, tests/test_integration_real_api.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test files
are updated or added to maintain test coverage and validate the changes.
"""

from typing import Optional, Dict, Any, List
from pathlib import Path
import sys
import json
import tempfile
from .interfaces import ResultsWriter
from .exceptions import FileSystemError
from .utils.file_utils import FileUtils
from .utils.logging_utils import LoggerMixin, log_async_function_call
from .utils.constants import Constants


class JSONResultsWriter(ResultsWriter, LoggerMixin):
    """JSON-based results writer for evaluation results."""
    
    def __init__(self, data_dir: str = Constants.DEFAULT_DATA_DIR):
        """Initialize results writer with data directory."""
        self.data_dir = data_dir
        self.logger.info(f"Initialized JSON results writer with data directory: {data_dir}")
    
    def write_results(self, results: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Write evaluation results to JSON file."""
        try:
            # Determine output path
            if output_path:
                final_path = Path(output_path)
            else:
                final_path = self._get_default_output_path(results['paper_id'])
            
            self.logger.debug(f"Writing results to: {final_path}")
            
            # Use results dictionary directly (already in correct format)
            results_dict = results.copy()
            
            # Ensure all required fields exist with defaults
            results_dict.setdefault('paper_id', 'unknown')
            results_dict.setdefault('TP', 0)
            results_dict.setdefault('FP', 0)
            results_dict.setdefault('FN', 0)
            results_dict.setdefault('precision', 0.0)
            results_dict.setdefault('recall', 0.0)
            results_dict.setdefault('f1', 0.0)
            
            # Convert detailed_results if they are Pydantic models
            if 'detailed_results' in results_dict and results_dict['detailed_results']:
                if hasattr(results_dict['detailed_results'][0], 'model_dump'):
                    results_dict['detailed_results'] = [r.model_dump() for r in results_dict['detailed_results']]
            
            # Ensure unmatched_gt_items is a list
            results_dict.setdefault('unmatched_gt_items', [])
            
            # Add metadata
            results_dict["_metadata"] = {
                "format_version": "1.0",
                "writer_type": "JSONResultsWriter"
            }
            
            # Write to file
            FileUtils.save_json_file(results_dict, final_path)
            
            self.logger.info(f"Successfully wrote results to: {final_path}")
            return str(final_path)
            
        except Exception as e:
            raise FileSystemError(f"Error writing results: {str(e)}")
    
    def _get_default_output_path(self, paper_id: str) -> Path:
        """Get default output path for a paper."""
        return Path(self.data_dir) / Constants.EVALUATIONS_DIR / f"{paper_id}_evaluation{Constants.JSON_EXT}"
    
    def initialize_incremental_file(self, paper_id: str, initial_data: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Initialize an incremental evaluation JSON file with initial structure."""
        try:
            # Determine output path
            if output_path:
                final_path = Path(output_path)
            else:
                final_path = self._get_default_output_path(paper_id)
            
            # Create initial structure
            initial_results = {
                "paper_id": paper_id,
                "evaluation_status": "in_progress",
                "TP": 0,
                "FP": 0, 
                "FN": 0,
                "precision": 0.0,
                "recall": 0.0,
                "f1": 0.0,
                "detailed_results": [],
                "unmatched_gt_items": initial_data.get('unmatched_gt_items', []),
                "_metadata": {
                    "format_version": "1.0",
                    "writer_type": "JSONResultsWriter",
                    "total_extractions": initial_data.get('total_extractions', 0),
                    "total_ground_truth": initial_data.get('total_ground_truth', 0),
                    "completed_extractions": 0
                }
            }
            
            # Add any additional fields from initial_data
            for key, value in initial_data.items():
                if key not in initial_results:
                    initial_results[key] = value
            
            # Write initial file
            self._write_incremental_safe(initial_results, final_path)
            
            self.logger.info(f"Initialized incremental evaluation file: {final_path}")
            return str(final_path)
            
        except Exception as e:
            raise FileSystemError(f"Error initializing incremental file: {str(e)}")
    
    def update_incremental_result(self, paper_id: str, new_result: Dict[str, Any], 
                                 current_metrics: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Update incremental evaluation file with a new result and updated metrics."""
        try:
            # Determine output path
            if output_path:
                final_path = Path(output_path)
            else:
                final_path = self._get_default_output_path(paper_id)
            
            # Load current file
            if not final_path.exists():
                raise FileSystemError(f"Incremental file does not exist: {final_path}")
            
            with open(final_path, 'r', encoding='utf-8') as f:
                current_data = json.load(f)
            
            # Add new result to detailed_results
            current_data['detailed_results'].append(new_result)
            
            # Update metrics
            current_data.update({
                "TP": current_metrics.get('TP', 0),
                "FP": current_metrics.get('FP', 0),
                "FN": current_metrics.get('FN', 0),
                "precision": current_metrics.get('precision', 0.0),
                "recall": current_metrics.get('recall', 0.0),
                "f1": current_metrics.get('f1', 0.0)
            })
            
            # Update metadata
            if '_metadata' in current_data:
                current_data['_metadata']['completed_extractions'] = len(current_data['detailed_results'])
            
            # Add any additional fields from current_metrics
            for key, value in current_metrics.items():
                if key not in ['TP', 'FP', 'FN', 'precision', 'recall', 'f1'] and key not in current_data:
                    current_data[key] = value
            
            # Write updated file
            self._write_incremental_safe(current_data, final_path)
            
            completed = len(current_data['detailed_results'])
            total = current_data.get('_metadata', {}).get('total_extractions', 0)
            self.logger.debug(f"Updated incremental file: {completed}/{total} extractions complete")
            
            return str(final_path)
            
        except Exception as e:
            raise FileSystemError(f"Error updating incremental file: {str(e)}")
    
    def finalize_incremental_file(self, paper_id: str, final_metrics: Dict[str, Any], 
                                 output_path: Optional[str] = None) -> str:
        """Finalize incremental evaluation file with completed status and final metrics."""
        try:
            # Determine output path
            if output_path:
                final_path = Path(output_path)
            else:
                final_path = self._get_default_output_path(paper_id)
            
            # Load current file
            if not final_path.exists():
                raise FileSystemError(f"Incremental file does not exist: {final_path}")
            
            with open(final_path, 'r', encoding='utf-8') as f:
                current_data = json.load(f)
            
            # Update to completed status
            current_data['evaluation_status'] = 'completed'
            
            # Update final metrics
            current_data.update(final_metrics)
            
            # Update metadata
            if '_metadata' in current_data:
                current_data['_metadata']['completed_extractions'] = len(current_data['detailed_results'])
            
            # Write final file
            self._write_incremental_safe(current_data, final_path)
            
            self.logger.info(f"Finalized incremental evaluation file: {final_path}")
            return str(final_path)
            
        except Exception as e:
            raise FileSystemError(f"Error finalizing incremental file: {str(e)}")
    
    def _write_incremental_safe(self, data: Dict[str, Any], output_path: Path) -> None:
        """Safely write data to file using atomic operation (temp file + rename)."""
        # Create parent directory if it doesn't exist
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write to temporary file first
        temp_dir = output_path.parent
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', 
                                       dir=temp_dir, delete=False, suffix='.tmp') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            temp_path = Path(f.name)
        
        # Atomic rename
        temp_path.replace(output_path)


class ConsoleResultsWriter(ResultsWriter, LoggerMixin):
    """Console-based results writer for displaying results."""
    
    def __init__(self, verbose: bool = False):
        """Initialize console results writer."""
        self.verbose = verbose
        self.logger.info("Initialized console results writer")
    
    def write_results(self, results: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Write evaluation results to console."""
        try:
            paper_id = results.get('paper_id', 'unknown')
            self.logger.debug(f"Writing results to console for paper: {paper_id}")
            
            # Print main results
            print(f"=== Evaluation Results for Paper {paper_id} ===")
            print(f"TP: {results.get('TP', 0)}, FP: {results.get('FP', 0)}, FN: {results.get('FN', 0)}")
            print(f"Precision: {results.get('precision', 0.0):.3f}")
            print(f"Recall: {results.get('recall', 0.0):.3f}")
            print(f"F1: {results.get('f1', 0.0):.3f}")
            
            # Show detailed results if verbose
            detailed_results = results.get('detailed_results', [])
            if self.verbose:
                print("\n=== Detailed Results ===")
                for i, detail in enumerate(detailed_results):
                    if isinstance(detail, dict):
                        llm_response = detail.get('llm_response', {})
                        summary = (
                            f"\nExtraction {i + 1}: {detail.get('classification', 'Unknown')}\n"
                            f"  Confidence: {llm_response.get('confidence', 0.0)}\n"
                            f"  Reason: {llm_response.get('reason', 'No reason provided')}\n"
                        )
                        if llm_response.get('reasoning'):
                            summary += f"  Reasoning: {llm_response.get('reasoning')}\n"
                    else:
                        # Handle legacy Pydantic models
                        summary = (
                            f"\nExtraction {i + 1}: {detail.classification}\n"
                            f"  Confidence: {detail.llm_response.confidence}\n"
                            f"  Reason: {detail.llm_response.reason}\n"
                        )
                        if detail.llm_response.reasoning:
                            summary += f"  Reasoning: {detail.llm_response.reasoning}\n"
                    
                    print(summary.encode('utf-8').decode(sys.stdout.encoding, errors='ignore'))
                
                unmatched_items = results.get('unmatched_gt_items', [])
                if unmatched_items:
                    print(f"\n=== Unmatched Ground Truth Items ({len(unmatched_items)}) ===")
                    for item in unmatched_items:
                        summary = f"  - {item.get('adc_name', 'Unknown')} / {item.get('model_name', 'Unknown')} / {item.get('endpoint_name', 'Unknown')}"
                        print(summary.encode('utf-8').decode(sys.stdout.encoding, errors='ignore'))
            
            return "console"
            
        except Exception as e:
            raise FileSystemError(f"Error writing results to console: {str(e)}")


class MultiResultsWriter(ResultsWriter, LoggerMixin):
    """Multi-format results writer that can write to multiple destinations."""
    
    def __init__(self, writers: Optional[list[ResultsWriter]] = None):
        """Initialize multi-format results writer."""
        self.writers = writers or [JSONResultsWriter(), ConsoleResultsWriter()]
        self.logger.info(f"Initialized multi-format results writer with {len(self.writers)} writers")
    
    def write_results(self, results: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """Write evaluation results using all configured writers."""
        try:
            output_paths = []
            
            for writer in self.writers:
                try:
                    path = writer.write_results(results, output_path)
                    output_paths.append(path)
                except Exception as e:
                    self.logger.error(f"Writer {type(writer).__name__} failed: {str(e)}")
                    # Continue with other writers
            
            if not output_paths:
                raise FileSystemError("All writers failed")
            
            return "; ".join(output_paths)
            
        except Exception as e:
            raise FileSystemError(f"Error writing results with multi-writer: {str(e)}")
    
    def add_writer(self, writer: ResultsWriter) -> None:
        """Add a new writer to the multi-writer."""
        self.writers.append(writer)
        self.logger.info(f"Added writer: {type(writer).__name__}")
    
    def remove_writer(self, writer_type: type) -> None:
        """Remove a writer by type."""
        self.writers = [w for w in self.writers if not isinstance(w, writer_type)]
        self.logger.info(f"Removed writer: {writer_type.__name__}")