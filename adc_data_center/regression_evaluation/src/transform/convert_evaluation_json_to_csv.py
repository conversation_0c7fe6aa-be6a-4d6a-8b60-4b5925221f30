"""
Converts evaluation JSON files into a flattened CSV format that mirrors the
original raw extraction CSV, plus additional evaluation columns.
"""
import pandas as pd
import json
import logging
from pathlib import Path
import sys
import argparse
from typing import Dict, Any, List, Optional

from datetime import datetime

# Ensure the script can find the 'src' directory
sys.path.append(str(Path(__file__).parent.parent.parent))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_required_columns() -> List[str]:
    """Returns the required columns in the specified order."""
    extraction_columns = [
        'id', 'adc_name', 'model_name', 'model_type', 'experiment_type',
        'endpoint_name', 'measured_value', 'measured_dose', 'measured_concentration',
        'measured_timepoint', 'measured_death_percentage', 'endpoint_citations'
    ]
    
    llm_columns = [
        'matched', 'gt_id', 'confidence', 'reason'
    ]
    
    return extraction_columns + llm_columns

# Removed reconstruction functions since we're taking data directly from extraction_row

def convert_json_to_flat_csv(input_dir: str, output_dir: str):
    """
    Reads all evaluation JSONs, aggregates them, and saves a single timestamped CSV.
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    json_files = list(input_path.glob("*_evaluation.json"))
    if not json_files:
        logging.warning(f"No evaluation JSON files found in {input_dir}")
        return

    logging.info(f"Found {len(json_files)} JSON files to convert.")

    # Get the required columns in the specified order
    final_headers = get_required_columns()
    
    all_csv_data = []

    # Second pass: extract data
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            detailed_results = data.get('detailed_results', [])
            if not detailed_results:
                logging.warning(f"No 'detailed_results' found in {json_file.name}. Skipping.")
                continue

            for result in detailed_results:
                extraction_row = result.get('extraction_row', {})
                llm_response = result.get('llm_response', {})
                
                # Extract only the specified extraction columns
                row = {}
                extraction_fields = [
                    'id', 'adc_name', 'model_name', 'model_type', 'experiment_type',
                    'endpoint_name', 'measured_value', 'measured_dose', 'measured_concentration',
                    'measured_timepoint', 'measured_death_percentage', 'endpoint_citations'
                ]
                
                for field in extraction_fields:
                    row[field] = extraction_row.get(field)
                
                # Add only the specified LLM response columns
                row.update({
                    'matched': llm_response.get('matched'),
                    'gt_id': llm_response.get('gt_id'),
                    'confidence': llm_response.get('confidence'),
                    'reason': llm_response.get('reason')
                })
                
                all_csv_data.append(row)
            logging.info(f"Processed {json_file.name}")

        except Exception as e:
            logging.error(f"Failed to process {json_file.name}. Error: {e}", exc_info=True)

    if not all_csv_data:
        logging.warning("No data was processed to create a CSV file.")
        return

    df = pd.DataFrame(all_csv_data)
    # Reorder columns to put extraction columns first, then evaluation columns
    df = df.reindex(columns=final_headers, fill_value='')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"evaluation_summary_{timestamp}.csv"
    csv_filepath = output_path / csv_filename
    df.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
    logging.info(f"Successfully created aggregated CSV: {csv_filepath}")

def main():
    parser = argparse.ArgumentParser(description="Convert evaluation JSON files to a flattened CSV format.")
    parser.add_argument(
        "--input-dir",
        default="data_no_annotations/evaluations/",
        help="Directory containing the evaluation JSON files."
    )
    parser.add_argument(
        "--output-dir",
        default="data_no_annotations/evaluations_CSV/",
        help="Directory to save the generated CSV files."
    )
    args = parser.parse_args()

    convert_json_to_flat_csv(args.input_dir, args.output_dir)

if __name__ == "__main__":
    main()