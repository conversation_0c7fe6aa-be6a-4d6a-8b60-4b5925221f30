import json
import argparse
import re
import os
import traceback

def normalize_string(s):
    try:
        if s is None:
            return None
        return re.sub(r'\s+', ' ', s.strip().lower())
    except Exception as e:
        print(f"Error normalizing string '{s}': {e}")
        return str(s) if s is not None else None

def normalize_endpoint_name(name):
    try:
        if name is None:
            return None
        name = normalize_string(name)
        mappings = {
            "adc ic??": "adc ic50",
            "adc_ic50": "adc ic50",
            "anti-tumor activity dose": "anti-tumor activity dose",
            "tumor growth inhibition": "tumor growth inhibition"
        }
        return mappings.get(name, name)
    except Exception as e:
        print(f"Error normalizing endpoint name '{name}': {e}")
        return str(name) if name is not None else None

def normalize_record(rec, error_log):
    try:
        # Ensure rec is a dictionary
        if not isinstance(rec, dict):
            error_log.append({
                "issue": "Invalid record format",
                "record": str(rec),
                "error": "Record is not a dictionary"
            })
            return {}
        
        # Create a copy to avoid modifying original
        normalized_rec = rec.copy()
        
        # Safely normalize each field
        try:
            normalized_rec['id'] = normalize_string(rec.get('id'))
        except Exception as e:
            error_log.append({
                "issue": "Error normalizing id field",
                "record_id": rec.get('id'),
                "error": str(e)
            })
            normalized_rec['id'] = str(rec.get('id')) if rec.get('id') is not None else None
        
        try:
            normalized_rec['adc_name'] = normalize_string(rec.get('adc_name'))
        except Exception as e:
            error_log.append({
                "issue": "Error normalizing adc_name field",
                "record_id": rec.get('id'),
                "error": str(e)
            })
            normalized_rec['adc_name'] = str(rec.get('adc_name')) if rec.get('adc_name') is not None else None
        
        try:
            normalized_rec['model_name'] = normalize_string(rec.get('model_name'))
        except Exception as e:
            error_log.append({
                "issue": "Error normalizing model_name field",
                "record_id": rec.get('id'),
                "error": str(e)
            })
            normalized_rec['model_name'] = str(rec.get('model_name')) if rec.get('model_name') is not None else None
        
        try:
            normalized_rec['endpoint_name'] = normalize_endpoint_name(rec.get('endpoint_name'))
        except Exception as e:
            error_log.append({
                "issue": "Error normalizing endpoint_name field",
                "record_id": rec.get('id'),
                "error": str(e)
            })
            normalized_rec['endpoint_name'] = str(rec.get('endpoint_name')) if rec.get('endpoint_name') is not None else None
        
        if normalized_rec.get('priority') is None:
            normalized_rec['priority'] = "prioritize"
        
        return normalized_rec
        
    except Exception as e:
        error_log.append({
            "issue": "Critical error in normalize_record",
            "record": str(rec),
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return rec  # Return original record if normalization fails completely

def check_similar_records(records, conflict_log, error_log):
    """
    Check for records with same key but different values - for logging purposes only.
    Does NOT remove duplicates, just logs potential conflicts for review.
    """
    try:
        key_groups = {}
        
        # Group records by key
        for i, r in enumerate(records):
            try:
                id_val = r.get('id')
                adc_name_val = r.get('adc_name')
                model_name_val = r.get('model_name')
                endpoint_name_val = r.get('endpoint_name')
                
                key = (id_val, adc_name_val, model_name_val, endpoint_name_val)
                
                if key not in key_groups:
                    key_groups[key] = []
                key_groups[key].append((i, r))
                
            except Exception as e:
                error_log.append({
                    "issue": "Error processing record for similarity check",
                    "record_index": i,
                    "record": str(r),
                    "error": str(e)
                })
        
        # Check for conflicts within each group
        for key, record_list in key_groups.items():
            if len(record_list) > 1:
                # Multiple records with same key - check for value differences
                try:
                    base_record = record_list[0][1]  # First record as reference
                    
                    for i in range(1, len(record_list)):
                        current_record = record_list[i][1]
                        record_index = record_list[i][0]
                        
                        # Check for differences in measurement fields
                        for field in ['measured_value','measured_timepoint','measured_concentration','measured_dose']:
                            if base_record.get(field) != current_record.get(field):
                                conflict_log.append({
                                    "issue": "Multiple records with same key but different values",
                                    "key": key,
                                    "field": field,
                                    "base_value": base_record.get(field),
                                    "current_value": current_record.get(field),
                                    "base_record_index": record_list[0][0],
                                    "current_record_index": record_index,
                                    "note": "Both records preserved - manual review recommended"
                                })
                except Exception as e:
                    error_log.append({
                        "issue": "Error comparing records with same key",
                        "key": key,
                        "error": str(e)
                    })
        
        return records  # Return all records unchanged
        
    except Exception as e:
        error_log.append({
            "issue": "Critical error in check_similar_records function",
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        return records

def detect_file_format(data, fname, error_log):
    """
    Detect whether the file is in GT format (dict with 'endpoints' key) 
    or result format (list of dictionaries)
    """
    try:
        if isinstance(data, dict) and 'endpoints' in data:
            return 'gt_format', data['endpoints']
        elif isinstance(data, list):
            return 'result_format', data
        elif isinstance(data, dict):
            # Check if it's a dict but without 'endpoints' key
            error_log.append({
                "issue": "Dictionary format but missing 'endpoints' key",
                "file": fname,
                "available_keys": list(data.keys())
            })
            return 'unknown', []
        else:
            error_log.append({
                "issue": "Unknown file format",
                "file": fname,
                "data_type": str(type(data))
            })
            return 'unknown', []
    except Exception as e:
        error_log.append({
            "issue": "Error detecting file format",
            "file": fname,
            "error": str(e)
        })
        return 'unknown', []

def load_json_files_from_dir(directory, error_log):
    data_dict = {}
    
    try:
        if not os.path.exists(directory):
            error_log.append({
                "issue": "Directory does not exist",
                "directory": directory
            })
            return data_dict
        
        if not os.path.isdir(directory):
            error_log.append({
                "issue": "Path is not a directory",
                "directory": directory
            })
            return data_dict
        
        files = os.listdir(directory)
        json_files = [f for f in files if f.endswith(".json")]
        
        if not json_files:
            error_log.append({
                "issue": "No JSON files found in directory",
                "directory": directory
            })
        
        for fname in json_files:
            try:
                path = os.path.join(directory, fname)
                with open(path, 'r', encoding='utf-8') as f:
                    data_dict[fname] = json.load(f)
                    print(f"Successfully loaded {fname}")
            except json.JSONDecodeError as e:
                error_log.append({
                    "issue": "JSON decode error",
                    "file": fname,
                    "error": str(e),
                    "line": getattr(e, 'lineno', 'unknown'),
                    "column": getattr(e, 'colno', 'unknown')
                })
                data_dict[fname] = None
                print(f"JSON decode error in {fname}: {e}")
            except FileNotFoundError as e:
                error_log.append({
                    "issue": "File not found",
                    "file": fname,
                    "error": str(e)
                })
                data_dict[fname] = None
                print(f"File not found {fname}: {e}")
            except PermissionError as e:
                error_log.append({
                    "issue": "Permission denied",
                    "file": fname,
                    "error": str(e)
                })
                data_dict[fname] = None
                print(f"Permission denied for {fname}: {e}")
            except Exception as e:
                error_log.append({
                    "issue": "Unexpected error loading file",
                    "file": fname,
                    "error": str(e),
                    "traceback": traceback.format_exc()
                })
                data_dict[fname] = None
                print(f"Failed to load {fname}: {e}")
                
    except Exception as e:
        error_log.append({
            "issue": "Critical error in load_json_files_from_dir",
            "directory": directory,
            "error": str(e),
            "traceback": traceback.format_exc()
        })
        print(f"Critical error loading files from {directory}: {e}")
    
    return data_dict

if __name__ == "__main__":
    try:
        parser = argparse.ArgumentParser(description="Normalize endpoint JSON files and check for potential conflicts.")
        parser.add_argument("--gt-dir", required=True, help="Directory containing GT JSON files")
        parser.add_argument("--result-dir", required=True, help="Directory containing result JSON files")
        args = parser.parse_args()

        # Initialize all error logs
        conflict_log = []
        error_log = []
        processing_log = []

        print(f"Loading files from GT directory: {args.gt_dir}")
        gt_files = load_json_files_from_dir(args.gt_dir, error_log)
        
        print(f"Loading files from result directory: {args.result_dir}")
        res_files = load_json_files_from_dir(args.result_dir, error_log)

        # Ensure result directory exists for log file
        try:
            os.makedirs(args.result_dir, exist_ok=True)
        except Exception as e:
            error_log.append({
                "issue": "Cannot create result directory",
                "directory": args.result_dir,
                "error": str(e)
            })
            print(f"Warning: Cannot create result directory {args.result_dir}: {e}")

        log_path = os.path.join(args.result_dir, "normalization_log.json")

        # Process GT files
        print("\nProcessing GT files...")
        gt_processed_count = 0
        for fname, data in gt_files.items():
            try:
                print(f"Processing GT file: {fname}")
                
                if data is None:
                    processing_log.append({
                        "issue": "Skipped GT file due to loading error",
                        "file": fname,
                        "file_type": "gt"
                    })
                    continue
                
                file_format, endpoints_data = detect_file_format(data, fname, error_log)
                
                if file_format == 'unknown':
                    processing_log.append({
                        "issue": "Unknown file format in GT file",
                        "file": fname,
                        "file_type": "gt"
                    })
                    continue
                
                if not isinstance(endpoints_data, list):
                    processing_log.append({
                        "issue": "Endpoints data is not a list in GT file",
                        "file": fname,
                        "file_type": "gt",
                        "endpoints_type": str(type(endpoints_data))
                    })
                    continue
                
                # Normalize records
                normalized_endpoints = []
                for i, endpoint in enumerate(endpoints_data):
                    try:
                        normalized = normalize_record(endpoint, error_log)
                        normalized_endpoints.append(normalized)
                    except Exception as e:
                        error_log.append({
                            "issue": "Error normalizing individual endpoint in GT file",
                            "file": fname,
                            "file_type": "gt",
                            "endpoint_index": i,
                            "error": str(e)
                        })
                        normalized_endpoints.append(endpoint)  # Keep original if normalization fails
                
                # Check for similar records (logging only, no removal)
                final_endpoints = check_similar_records(normalized_endpoints, conflict_log, error_log)
                
                # Save GT file (always in GT format)
                file_path = os.path.join(args.gt_dir, fname)
                try:
                    with open(file_path, "w", encoding='utf-8') as f:
                        json.dump({"endpoints": final_endpoints}, f, indent=2, ensure_ascii=False)
                    gt_processed_count += 1
                    print(f"Successfully processed and saved GT file {fname} ({len(final_endpoints)} records)")
                except Exception as e:
                    error_log.append({
                        "issue": "Error saving processed GT file",
                        "file": fname,
                        "file_type": "gt",
                        "file_path": file_path,
                        "error": str(e)
                    })
                    print(f"Error saving GT file {fname}: {e}")
                    
            except Exception as e:
                error_log.append({
                    "issue": "Critical error processing GT file",
                    "file": fname,
                    "file_type": "gt",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                })
                print(f"Critical error processing GT file {fname}: {e}")

        # Process Result files
        print("\nProcessing Result files...")
        res_processed_count = 0
        for fname, data in res_files.items():
            try:
                print(f"Processing Result file: {fname}")
                
                if data is None:
                    processing_log.append({
                        "issue": "Skipped Result file due to loading error",
                        "file": fname,
                        "file_type": "result"
                    })
                    continue
                
                file_format, endpoints_data = detect_file_format(data, fname, error_log)
                
                if file_format == 'unknown':
                    processing_log.append({
                        "issue": "Unknown file format in Result file",
                        "file": fname,
                        "file_type": "result"
                    })
                    continue
                
                if not isinstance(endpoints_data, list):
                    processing_log.append({
                        "issue": "Endpoints data is not a list in Result file",
                        "file": fname,
                        "file_type": "result",
                        "endpoints_type": str(type(endpoints_data))
                    })
                    continue
                
                # Normalize records
                normalized_endpoints = []
                for i, endpoint in enumerate(endpoints_data):
                    try:
                        normalized = normalize_record(endpoint, error_log)
                        normalized_endpoints.append(normalized)
                    except Exception as e:
                        error_log.append({
                            "issue": "Error normalizing individual endpoint in Result file",
                            "file": fname,
                            "file_type": "result",
                            "endpoint_index": i,
                            "error": str(e)
                        })
                        normalized_endpoints.append(endpoint)  # Keep original if normalization fails
                
                # Check for similar records (logging only, no removal)
                final_endpoints = check_similar_records(normalized_endpoints, conflict_log, error_log)
                
                # Save Result file (preserve original format - list format)
                file_path = os.path.join(args.result_dir, fname)
                try:
                    with open(file_path, "w", encoding='utf-8') as f:
                        if file_format == 'result_format':
                            # Save as list (original result format)
                            json.dump(final_endpoints, f, indent=2, ensure_ascii=False)
                        else:
                            # Save as GT format if it was originally GT format
                            json.dump({"endpoints": final_endpoints}, f, indent=2, ensure_ascii=False)
                    res_processed_count += 1
                    print(f"Successfully processed and saved Result file {fname} ({len(final_endpoints)} records)")
                except Exception as e:
                    error_log.append({
                        "issue": "Error saving processed Result file",
                        "file": fname,
                        "file_type": "result",
                        "file_path": file_path,
                        "error": str(e)
                    })
                    print(f"Error saving Result file {fname}: {e}")
                    
            except Exception as e:
                error_log.append({
                    "issue": "Critical error processing Result file",
                    "file": fname,
                    "file_type": "result",
                    "error": str(e),
                    "traceback": traceback.format_exc()
                })
                print(f"Critical error processing Result file {fname}: {e}")

        # Compile all logs
        all_logs = []
        all_logs.extend([{"log_type": "error", **log} for log in error_log])
        all_logs.extend([{"log_type": "conflict", **log} for log in conflict_log])
        all_logs.extend([{"log_type": "processing", **log} for log in processing_log])
        
        # Add summary
        summary = {
            "log_type": "summary",
            "gt_files_found": len(gt_files),
            "result_files_found": len(res_files),
            "gt_files_processed_successfully": gt_processed_count,
            "result_files_processed_successfully": res_processed_count,
            "total_errors": len(error_log),
            "total_conflicts": len(conflict_log),
            "total_processing_issues": len(processing_log),
            "note": "No deduplication performed - all records preserved"
        }
        all_logs.insert(0, summary)

        # Save comprehensive log
        try:
            with open(log_path, "w", encoding='utf-8') as f:
                json.dump(all_logs, f, indent=2, ensure_ascii=False)
            print(f"Comprehensive logs saved to {log_path}")
        except Exception as e:
            print(f"Error saving log file: {e}")
            # Try to save to a backup location
            try:
                backup_log_path = "normalization_log_backup.json"
                with open(backup_log_path, "w", encoding='utf-8') as f:
                    json.dump(all_logs, f, indent=2, ensure_ascii=False)
                print(f"Logs saved to backup location: {backup_log_path}")
            except Exception as backup_e:
                print(f"Failed to save backup log: {backup_e}")

        print(f"\nNormalization complete!")
        print(f"Summary:")
        print(f"- GT files found: {len(gt_files)}")
        print(f"- Result files found: {len(res_files)}")
        print(f"- GT files processed successfully: {gt_processed_count}")
        print(f"- Result files processed successfully: {res_processed_count}")
        print(f"- Total errors logged: {len(error_log)}")
        print(f"- Total conflicts found: {len(conflict_log)}")
        print(f"- Processing issues: {len(processing_log)}")
        
    except Exception as e:
        print(f"Critical script error: {e}")
        print(traceback.format_exc())
        exit(1)