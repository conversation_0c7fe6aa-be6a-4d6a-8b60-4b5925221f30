"""
Unified ground truth converter for ADC evaluation data.

This module provides a single converter for ground truth CSV files that works for both
annotated and unannotated datasets. Ground truth structure is consistent regardless
of whether the corresponding extraction results are annotated or unannotated.

Key features:
- Single converter that handles different CSV column variations
- Consistent output structure matching existing ground truth format
- Standardized field mapping to extraction format names
- Proper handling of missing values and data validation

TEST FILE: tests/test_unified_ground_truth_converter.py
"""

import pandas as pd
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any
from regression_evaluation.src.utils.logging_utils import get_logger
from regression_evaluation.src.models import GroundTruthDocument, GTEndpoint

logger = get_logger(__name__)

class UnifiedGroundTruthConverter:
    """Unified converter for ground truth CSV files."""
    
    def __init__(self, csv_path: str):
        self.csv_path = Path(csv_path).resolve()
        self.df: Optional[pd.DataFrame] = None
        self.priority_mapping: Optional[Dict[str, List[str]]] = None
    
    def load_csv_file(self):
        """Load the ground truth CSV file."""
        try:
            if not self.csv_path.exists():
                raise FileNotFoundError(f"No CSV file found at {self.csv_path}")
            
            self.df = pd.read_csv(self.csv_path, encoding='utf-8-sig')
            
            # Replace NaN values with None for clean JSON output
            self.df = self.df.replace({np.nan: None})
            
            # Clean up column names by stripping leading/trailing whitespace
            self.df.columns = self.df.columns.str.strip()
            
            # Handle both 'Id' and 'id' column names for paper ID
            if 'Id' in self.df.columns and 'id' not in self.df.columns:
                self.df.rename(columns={'Id': 'id'}, inplace=True)
                logger.info("Renamed 'Id' column to 'id' for consistency")
            
            logger.info(f"Loaded {len(self.df)} ground truth records from {self.csv_path.name}")
            logger.info(f"Available columns: {list(self.df.columns)}")

        except Exception as e:
            logger.error(f"Error loading CSV file: {e}")
            raise
    
    def load_priority_mapping(self):
        """Load priority mapping from JSON file in the same directory as CSV."""
        try:
            # Look for priority_mapping.json in the same directory as the CSV
            priority_json_path = self.csv_path.parent / 'priority_mapping.json'
            
            if priority_json_path.exists():
                with open(priority_json_path, 'r', encoding='utf-8') as f:
                    priority_data = json.load(f)
                    self.priority_mapping = priority_data.get('priority_mapping_ground_truth', priority_data.get('priority_mapping', {}))
                    logger.info(f"Loaded priority mapping with {len(self.priority_mapping)} categories")
            else:
                logger.warning(f"Priority mapping file not found at {priority_json_path}")
                self.priority_mapping = {}
                
        except Exception as e:
            logger.error(f"Error loading priority mapping: {e}")
            self.priority_mapping = {}
    
    def get_endpoint_priority(self, endpoint_name: str) -> Optional[str]:
        """Get priority for a given endpoint name."""
        if not self.priority_mapping:
            return None
            
        # Search through all priority categories
        for priority, endpoints in self.priority_mapping.items():
            if endpoint_name in endpoints:
                return priority
                
        return None
    
    def get_unique_paper_ids(self) -> List[str]:
        """Get list of unique paper IDs in the dataset."""
        if self.df is None:
            raise ValueError("CSV file not loaded. Call load_csv_file() first.")
        
        unique_ids = self.df['id'].unique().tolist()
        unique_ids = [str(pid).strip() for pid in unique_ids]
        logger.info(f"Found {len(unique_ids)} unique paper IDs")
        return unique_ids
    
    def process_paper(self, paper_id: str) -> GroundTruthDocument:
        """Process a single paper and return ground truth document."""
        if self.df is None:
            raise ValueError("CSV file not loaded. Call load_csv_file() first.")
        
        # Filter data for this paper
        paper_data = self.df[self.df['id'] == str(paper_id)]
        
        if paper_data.empty:
            logger.warning(f"No data found for paper {paper_id}")
            return GroundTruthDocument(id=paper_id, endpoints=[])
        
        endpoints = []
        for _, row in paper_data.iterrows():
            try:
                # Convert row to GTEndpoint
                endpoint = self.convert_row_to_endpoint(row, paper_id)
                if endpoint:  # Only add if valid
                    endpoints.append(endpoint)
                
            except Exception as e:
                logger.error(f"Error processing row for paper {paper_id}: {e}")
                continue
        
        logger.info(f"Processed {len(endpoints)} ground truth endpoints for paper {paper_id}")
        return GroundTruthDocument(id=paper_id, endpoints=endpoints)
    
    def convert_row_to_endpoint(self, row: pd.Series, paper_id: str) -> Optional[GTEndpoint]:
        """Convert a single CSV row to GTEndpoint format."""
        
        row_dict = row.to_dict()
        
        # Skip rows with missing model_name
        if pd.isna(row_dict.get("model_name")) or row_dict.get("model_name") == "N/A":
            logger.warning(f"Skipping row for paper {paper_id} due to missing or N/A model_name.")
            return None
        
        # Detect dataset type based on available columns
        # Unannotated datasets typically have different column names
        has_measured_value = "endpoint_measured_value" in row_dict
        has_endpoint_value = "endpoint_value" in row_dict
        
        # Determine which columns to use based on what's available
        if has_measured_value:
            # Unannotated dataset column names
            measured_value = row_dict.get("endpoint_measured_value")
            measured_timepoint = row_dict.get("endpoint_measured_timepoint")  
            measured_concentration = row_dict.get("treatment_entity_concentration")
            measured_dose = row_dict.get("treatment_entity_dose")
            measured_death_percentage = row_dict.get("percent_death")
        elif has_endpoint_value:
            # Annotated dataset column names
            measured_value = row_dict.get("endpoint_value")
            measured_timepoint = row_dict.get("endpoint_timepoint")
            measured_concentration = row_dict.get("endpoint_concentration")
            measured_dose = None  # Not available in annotated
            measured_death_percentage = None  # Not available in annotated
        else:
            # Fallback - try both and use whichever is available
            measured_value = row_dict.get("endpoint_value") or row_dict.get("endpoint_measured_value")
            measured_timepoint = row_dict.get("endpoint_timepoint") or row_dict.get("endpoint_measured_timepoint")
            measured_concentration = row_dict.get("endpoint_concentration") or row_dict.get("treatment_entity_concentration")
            measured_dose = row_dict.get("treatment_entity_dose")
            measured_death_percentage = row_dict.get("percent_death")
        
        # Get priority for this endpoint
        endpoint_name = row_dict.get("endpoint_name")
        priority = self.get_endpoint_priority(endpoint_name) if endpoint_name else None
        
        # Create standardized endpoint data
        endpoint_data = {
            "id": paper_id,
            "adc_name": row_dict.get("adc_name"),
            "model_name": row_dict.get("model_name"),
            "model_type": row_dict.get("model_type"),
            "experiment_type": row_dict.get("experiment_type"),
            "endpoint_name": endpoint_name,
            "measured_value": measured_value,
            "measured_timepoint": measured_timepoint,
            "measured_concentration": measured_concentration,
            "measured_dose": measured_dose,
            "measured_death_percentage": measured_death_percentage,
            "endpoint_units": row_dict.get("endpoint_units"),
            "priority": priority,
        }
        
        try:
            return GTEndpoint(**endpoint_data)
        except Exception as e:
            logger.error(f"Error creating GTEndpoint for paper {paper_id}: {e}")
            logger.error(f"Endpoint data: {endpoint_data}")
            return None
    
    def save_ground_truth_files(self, output_dir: str, validate_only: bool = False) -> Dict[str, int]:
        """Save ground truth JSON files for all papers."""
        if self.df is None:
            raise ValueError("CSV file not loaded. Call load_csv_file() first.")
        
        output_path = Path(output_dir).resolve()
        if not validate_only:
            output_path.mkdir(parents=True, exist_ok=True)
        
        paper_ids = self.get_unique_paper_ids()
        stats = {"processed": 0, "skipped": 0, "errors": 0}
        
        for paper_id in paper_ids:
            try:
                # Process paper data
                ground_truth_doc = self.process_paper(paper_id)
                
                if not ground_truth_doc.endpoints:
                    logger.warning(f"No valid ground truth endpoints for paper {paper_id}")
                    stats["skipped"] += 1
                    continue
                
                if not validate_only:
                    # Save to file
                    output_file = output_path / f"{paper_id.lower()}_gt.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(ground_truth_doc.model_dump(), f, indent=2, ensure_ascii=False)
                    
                    logger.info(f"Saved {len(ground_truth_doc.endpoints)} endpoints to {output_file.name}")
                
                stats["processed"] += 1
                
            except Exception as e:
                logger.error(f"Error processing paper {paper_id}: {e}")
                stats["errors"] += 1
                continue
        
        logger.info(f"Processing complete. Processed: {stats['processed']}, "
                   f"Skipped: {stats['skipped']}, Errors: {stats['errors']}")
        
        return stats