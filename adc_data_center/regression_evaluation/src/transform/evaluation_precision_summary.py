#!/usr/bin/env python3
"""
Create precision summary CSV from evaluation JSON files.

This script extracts key metrics from all evaluation JSON files and creates
a summary CSV with per-paper precision, endpoint counts, and confusion matrix data.

Usage:
    python -m src.transform.evaluation_precision_summary [--input-dir DIR] [--output-dir DIR]
"""

import json
import os
import pandas as pd
import argparse
from datetime import datetime
from pathlib import Path


def create_precision_summary(input_dir: str, output_dir: str) -> str:
    """
    Create precision summary CSV from evaluation JSON files.
    
    Args:
        input_dir: Directory containing evaluation JSON files
        output_dir: Directory to save the output CSV
        
    Returns:
        Path to the created CSV file
    """
    
    # Load all evaluation files and extract precision data
    results = []

    for file in sorted(os.listdir(input_dir)):
        if file.endswith('_evaluation.json'):
            paper_id = file.replace('_evaluation.json', '')
            with open(os.path.join(input_dir, file), 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            precision = data.get('precision', 0)
            tp = data.get('TP', 0)
            fp = data.get('FP', 0)
            fn = data.get('FN', 0)
            endpoint_count = tp + fp  # Total endpoints evaluated for this paper
            
            results.append({
                'id': paper_id,
                'precision': precision,
                'endpoint_count': endpoint_count,
                'TPs': tp,
                'FPs': fp,
                'FNs': fn
            })

    # Create DataFrame
    df = pd.DataFrame(results)

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(output_dir, f'precision_summary_{timestamp}.csv')

    # Save to CSV
    df.to_csv(output_file, index=False)

    print(f'Precision summary saved to: {output_file}')
    print(f'Total papers: {len(df)}')
    print(f'Columns: {list(df.columns)}')
    
    return output_file


def main():
    """Main function for CLI usage."""
    parser = argparse.ArgumentParser(
        description="Create precision summary CSV from evaluation JSON files"
    )
    parser.add_argument(
        '--input-dir', 
        default='data_04_scalup_no_annotations/evaluations',
        help='Directory containing evaluation JSON files (default: data_04_scalup_no_annotations/evaluations)'
    )
    parser.add_argument(
        '--output-dir',
        default='data_04_scalup_no_annotations/evaluations_CSV', 
        help='Output directory for CSV file (default: data_04_scalup_no_annotations/evaluations_CSV)'
    )
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_dir):
        print(f"Error: Input directory does not exist: {args.input_dir}")
        return 1
        
    try:
        output_file = create_precision_summary(args.input_dir, args.output_dir)
        print(f"Success: Precision summary created at {output_file}")
        return 0
    except Exception as e:
        print(f"Error creating precision summary: {e}")
        return 1


if __name__ == "__main__":
    exit(main())