"""
Unified CLI for converting ground truth CSV to JSON format.

This script provides a single command-line interface for converting ground truth CSV
files into the standardized JSON format required by the evaluation system.
Works for both annotated and unannotated datasets.

Usage:
    # Convert ground truth CSV to JSON
    python -m src.transform.unified_ground_truth_cli --csv-path data/raw_groundtruth/groundtruth.csv --output-dir data/ground-truth/
"""

import argparse
import sys
from pathlib import Path
from .unified_ground_truth_converter import UnifiedGroundTruthConverter
from ..utils.logging_utils import get_logger

logger = get_logger(__name__)

def main():
    """Main CLI function for unified ground truth conversion."""
    parser = argparse.ArgumentParser(
        description="Unified converter for ground truth CSV files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Convert ground truth CSV
  python -m src.transform.unified_ground_truth_cli --csv-path data_04_scalup_no_annotations/raw_groundtruth/groundtruth.csv --output-dir data_04_scalup_no_annotations/ground-truth/

  # Validate without saving
  python -m src.transform.unified_ground_truth_cli --csv-path data/raw_groundtruth/groundtruth.csv --output-dir data/ground-truth/ --validate-only
        """
    )
    
    parser.add_argument(
        '--csv-path',
        required=True,
        help='Path to ground truth CSV file'
    )
    
    parser.add_argument(
        '--output-dir', 
        required=True,
        help='Directory to save the per-paper JSON files'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Validate transformation without saving files'
    )
    
    parser.add_argument(
        '--paper-ids',
        nargs='*',
        help='Process only specific paper IDs (optional)'
    )
    
    args = parser.parse_args()
    
    try:
        # Validate input file exists
        csv_path = Path(args.csv_path)
        if not csv_path.exists():
            logger.error(f"CSV file not found: {csv_path}")
            sys.exit(1)
        
        # Validate file type
        if not csv_path.suffix.lower() == '.csv':
            logger.error("Input file must be a CSV file (.csv)")
            sys.exit(1)
        
        # Initialize converter
        logger.info("Initializing ground truth converter")
        converter = UnifiedGroundTruthConverter(csv_path=str(csv_path))
        
        # Load CSV file
        logger.info("Loading CSV file...")
        converter.load_csv_file()
        
        # Load priority mapping
        logger.info("Loading priority mapping...")
        converter.load_priority_mapping()
        
        # Get available paper IDs
        available_papers = converter.get_unique_paper_ids()

        # Determine which papers to process
        if args.paper_ids:
            papers_to_process = []
            for paper_id in args.paper_ids:
                if paper_id in available_papers:
                    papers_to_process.append(paper_id)
                else:
                    logger.warning(f"Paper ID {paper_id} not found in data")
            
            if not papers_to_process:
                logger.error("No valid paper IDs found")
                sys.exit(1)
        else:
            papers_to_process = available_papers

        logger.info(f"Processing {len(papers_to_process)} papers")
        
        # Process papers
        if args.validate_only:
            logger.info("Validation mode - no files will be saved")
        
        # Temporarily filter converter to only process specified papers
        if args.paper_ids:
            converter.df = converter.df[converter.df['id'].isin(papers_to_process)]
        
        # Save results
        stats = converter.save_ground_truth_files(
            output_dir=args.output_dir,
            validate_only=args.validate_only
        )
        
        # Report results
        logger.info(f"""
Conversion complete!
- Processed: {stats['processed']} papers
- Skipped: {stats['skipped']} papers  
- Errors: {stats['errors']} papers
- Output: {args.output_dir if not args.validate_only else 'Validation only'}
        """)
        
        if stats['errors'] > 0:
            logger.warning("Some papers had errors during processing. Check logs for details.")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()