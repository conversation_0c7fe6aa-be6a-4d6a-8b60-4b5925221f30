"""
Unified CLI for converting extraction results to JSON format.

This script provides a single command-line interface for converting both annotated 
(Excel) and unannotated (CSV) extraction results into the standardized JSON format
required by the evaluation system.

Usage:
    # Convert unannotated CSV extraction results
    python -m src.transform.unified_extraction_cli --input-file data.csv --output-dir results/ 
    
    # Convert annotated Excel extraction results
    python -m src.transform.unified_extraction_cli --input-file data.xlsx --output-dir results/ --annotated --sheet-name Endpoints
"""

import argparse
import sys
from pathlib import Path
from src.transform.unified_extraction_converter import UnifiedExtractionConverter
from src.utils.logging_utils import get_logger

logger = get_logger(__name__)

def main():
    """Main CLI function for unified extraction conversion."""
    parser = argparse.ArgumentParser(
        description="Unified converter for both annotated and unannotated extraction results",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Convert unannotated CSV file
  python -m src.transform.unified_extraction_cli --input-file data_04_scalup_no_annotations/raw_extraction/results.csv --output-dir data_04_scalup_no_annotations/extraction_results/

  # Convert annotated Excel file  
  python -m src.transform.unified_extraction_cli --input-file data_scaleup_annotated/raw_extraction_results/results.xlsx --output-dir data_scaleup_annotated/extraction_results/ --annotated --sheet-name Endpoints
        """
    )
    
    parser.add_argument(
        '--input-file',
        required=True,
        help='Path to input file (CSV for unannotated, Excel for annotated)'
    )
    
    parser.add_argument(
        '--output-dir', 
        required=True,
        help='Directory to save the per-paper JSON files'
    )
    
    parser.add_argument(
        '--annotated',
        action='store_true',
        help='Set this flag if input contains human annotations (Excel format expected)'
    )
    
    parser.add_argument(
        '--sheet-name',
        default='Endpoints',
        help='Excel sheet name to process (only used with --annotated flag)'
    )
    
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Validate transformation without saving files'
    )
    
    parser.add_argument(
        '--paper-ids',
        nargs='*',
        help='Process only specific paper IDs (optional)'
    )
    
    args = parser.parse_args()
    
    try:
        # Validate input file exists
        input_path = Path(args.input_file)
        if not input_path.exists():
            logger.error(f"Input file not found: {input_path}")
            sys.exit(1)
        
        # Validate file type matches annotation flag
        if args.annotated and not input_path.suffix.lower() in ['.xlsx', '.xls']:
            logger.error("--annotated flag requires Excel file (.xlsx or .xls)")
            sys.exit(1)
        elif not args.annotated and not input_path.suffix.lower() == '.csv':
            logger.error("Unannotated data requires CSV file (.csv)")
            sys.exit(1)
        
        # Initialize converter
        logger.info(f"Initializing {'annotated' if args.annotated else 'unannotated'} extraction converter")
        converter = UnifiedExtractionConverter(
            file_path=str(input_path),
            annotated=args.annotated,
            sheet_name=args.sheet_name
        )
        
        # Load data file
        logger.info("Loading data file...")
        converter.load_data_file()
        
        # Load priority mapping
        logger.info("Loading priority mapping...")
        converter.load_priority_mapping()
        
        # Get available paper IDs
        available_papers = converter.get_unique_paper_ids()
        
        # Determine which papers to process
        if args.paper_ids:
            papers_to_process = []
            for paper_id in args.paper_ids:
                if paper_id in available_papers:
                    papers_to_process.append(paper_id)
                else:
                    logger.warning(f"Paper ID {paper_id} not found in data")
            
            if not papers_to_process:
                logger.error("No valid paper IDs found")
                sys.exit(1)
        else:
            papers_to_process = available_papers
        
        logger.info(f"Processing {len(papers_to_process)} papers")
        
        # Process papers
        if args.validate_only:
            logger.info("Validation mode - no files will be saved")
        
        # Temporarily filter converter to only process specified papers
        if args.paper_ids:
            converter.df = converter.df[converter.df['id'].isin(papers_to_process)]
        
        # Save results
        stats = converter.save_extraction_results(
            output_dir=args.output_dir,
            validate_only=args.validate_only
        )
        
        # Report results
        logger.info(f"""
Conversion complete!
- Processed: {stats['processed']} papers
- Skipped: {stats['skipped']} papers  
- Errors: {stats['errors']} papers
- Data type: {'Annotated' if args.annotated else 'Unannotated'}
- Output: {args.output_dir if not args.validate_only else 'Validation only'}
        """)
        
        if stats['errors'] > 0:
            logger.warning("Some papers had errors during processing. Check logs for details.")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()