"""
Test for the reasoning_for_inclusion field in ExperimentalModel.
"""
import pytest
from pydantic import ValidationError
from src.utils.extraction_pydantic_models import ExperimentalModel


def test_experimental_model_with_reasoning_for_inclusion():
    """Test that ExperimentalModel accepts and validates reasoning_for_inclusion field."""
    
    # Test data with all required fields including reasoning_for_inclusion
    model_data = {
        "citations": [
            "The ADC was tested on HER2-positive breast cancer cell line SK-BR-3.",
            "SK-BR-3 cells express high levels of HER2 antigen."
        ],
        "model_name": "SK-BR-3",
        "clinical_human_related": False,
        "cancer_type": "Breast Cancer",
        "cancer_subtype": "HER2-positive breast cancer",
        "investigative": True,
        "reasoning_for_inclusion": "SK-BR-3 was selected as it is a well-established HER2-positive breast cancer cell line with high antigen expression, making it ideal for evaluating the binding and cytotoxic effects of HER2-targeting ADCs. The model provides a reliable platform for assessing ADC efficacy in a clinically relevant cancer type."
    }
    
    # Create ExperimentalModel instance
    model = ExperimentalModel(**model_data)
    
    # Verify all fields are properly set
    assert model.citations == model_data["citations"]
    assert model.model_name == model_data["model_name"]
    assert model.clinical_human_related == model_data["clinical_human_related"]
    assert model.cancer_type == model_data["cancer_type"]
    assert model.cancer_subtype == model_data["cancer_subtype"]
    assert model.investigative == model_data["investigative"]
    assert model.reasoning_for_inclusion == model_data["reasoning_for_inclusion"]


def test_experimental_model_missing_reasoning_for_inclusion():
    """Test that ExperimentalModel requires reasoning_for_inclusion field."""
    
    # Test data missing reasoning_for_inclusion
    model_data = {
        "citations": ["Test citation"],
        "model_name": "Test Model",
        "clinical_human_related": False,
        "cancer_type": "Test Cancer",
        "investigative": True
        # Missing reasoning_for_inclusion
    }
    
    # Should raise ValidationError due to missing required field
    with pytest.raises(ValidationError) as exc_info:
        ExperimentalModel(**model_data)
    
    # Check that the error mentions the missing field
    assert "reasoning_for_inclusion" in str(exc_info.value)


def test_experimental_model_serialization():
    """Test that ExperimentalModel properly serializes with reasoning_for_inclusion."""
    
    model_data = {
        "citations": ["Test citation"],
        "model_name": "Test Model",
        "clinical_human_related": False,
        "cancer_type": "Test Cancer",
        "investigative": True,
        "reasoning_for_inclusion": "This model was selected for its relevance to the study objectives."
    }
    
    model = ExperimentalModel(**model_data)
    
    # Test model_dump includes reasoning_for_inclusion
    dumped = model.model_dump()
    assert "reasoning_for_inclusion" in dumped
    assert dumped["reasoning_for_inclusion"] == model_data["reasoning_for_inclusion"]
    
    # Test model_dump_json includes reasoning_for_inclusion
    json_str = model.model_dump_json()
    assert "reasoning_for_inclusion" in json_str
    assert model_data["reasoning_for_inclusion"] in json_str


def test_reasoning_for_inclusion_field_description():
    """Test that the reasoning_for_inclusion field has the expected description."""
    
    # Get the field info for reasoning_for_inclusion
    field_info = ExperimentalModel.model_fields["reasoning_for_inclusion"]
    
    # Check that the description contains key concepts
    description = field_info.description
    assert "reasoning" in description.lower()
    assert "criteria" in description.lower()
    assert "selected" in description.lower()
    assert "extraction" in description.lower()
    assert "model" in description.lower()


if __name__ == "__main__":
    # Run the tests
    test_experimental_model_with_reasoning_for_inclusion()
    test_experimental_model_missing_reasoning_for_inclusion()
    test_experimental_model_serialization()
    test_reasoning_for_inclusion_field_description()
    print("All tests passed!")
