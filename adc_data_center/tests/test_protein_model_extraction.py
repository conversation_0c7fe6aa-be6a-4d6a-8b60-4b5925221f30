"""
Test for protein-based model extraction and enhanced model context.
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.utils.extraction_pydantic_models import ExperimentalModel


def test_protein_based_model_creation():
    """Test that protein-based experimental models can be properly created."""
    
    # Test HER2 ECD protein model (binding assay)
    protein_model = ExperimentalModel(
        citations=[
            "For a binding assay, immunoplates were coated with 2.5 mg/mL His-tagged HER2-ECD protein (Sino Biological Inc.)",
            "Kd values of DS-8201a and the anti-HER2 Ab were determined by ELISA using HER2 ECD protein"
        ],
        model_name="His-tagged HER2-ECD protein",
        clinical_human_related=False,
        cancer_type="Protein-based binding assay",
        investigative=True,
        reasoning_for_inclusion="His-tagged HER2-ECD protein was used as the target protein in ELISA-based binding assays to determine the binding affinity (Kd) of DS-8201a to HER2. This protein-based system allows for direct measurement of antibody-antigen binding kinetics without the complexity of cellular systems, providing accurate binding affinity measurements essential for characterizing the ADC's target engagement properties."
    )
    
    # Verify the model is properly created
    assert protein_model.model_name == "His-tagged HER2-ECD protein"
    assert protein_model.cancer_type == "Protein-based binding assay"
    assert protein_model.investigative == True
    assert "binding affinity" in protein_model.reasoning_for_inclusion
    assert "ELISA" in protein_model.reasoning_for_inclusion
    
    # Test cell-based model for comparison
    cell_model = ExperimentalModel(
        citations=[
            "SK-BR-3 cells were preincubated in a 96-well plate for 4 days",
            "DS-8201a induced the downregulation of the intracellular pAkt (Ser473) in the SK-BR-3 cells"
        ],
        model_name="SK-BR-3",
        clinical_human_related=False,
        cancer_type="Breast Cancer",
        cancer_subtype="HER2-positive breast cancer",
        investigative=True,
        reasoning_for_inclusion="SK-BR-3 cells were selected for cellular assays including cytotoxicity, ADCC activity, and Akt phosphorylation studies due to their high HER2 expression levels, making them ideal for evaluating cellular responses to HER2-targeting ADCs."
    )
    
    # Verify the models are different and serve different purposes
    assert protein_model.model_name != cell_model.model_name
    assert protein_model.cancer_type != cell_model.cancer_type
    assert "binding affinity" in protein_model.reasoning_for_inclusion
    assert "cellular" in cell_model.reasoning_for_inclusion


def test_enhanced_context_for_protein_models():
    """Test that enhanced model context properly handles protein-based models."""
    
    # Create models representing the w2314772071.md paper scenario
    protein_model = ExperimentalModel(
        citations=[
            "Kd values of DS-8201a and the anti-HER2 Ab were determined by ELISA using HER2 ECD protein",
            "immunoplates were coated with 2.5 mg/mL His-tagged HER2-ECD protein"
        ],
        model_name="His-tagged HER2-ECD protein",
        clinical_human_related=False,
        cancer_type="Protein-based binding assay",
        investigative=True,
        reasoning_for_inclusion="Used for direct binding affinity measurements (Kd) in ELISA format to assess ADC binding to HER2 target without cellular interference."
    )
    
    cell_model = ExperimentalModel(
        citations=[
            "SK-BR-3 cells were preincubated in a 96-well plate for 4 days",
            "DS-8201a showed ADCC activity, resulting in 48.6% of maximum cytotoxicity with an EC50 of 3.8 ng/mL"
        ],
        model_name="SK-BR-3",
        clinical_human_related=False,
        cancer_type="Breast Cancer",
        cancer_subtype="HER2-positive breast cancer",
        investigative=True,
        reasoning_for_inclusion="Used for cellular assays including ADCC activity and cytotoxicity measurements due to high HER2 expression."
    )
    
    models = [protein_model, cell_model]
    
    # Simulate enhanced context creation (same logic as in extract_endpoints)
    model_contexts = []
    model_names = []
    
    for model in models:
        model_names.append(model.model_name)
        
        # Build comprehensive model context
        context_parts = [f"Model Name: {model.model_name}"]
        
        if model.cancer_type and model.cancer_type != "NONE":
            context_parts.append(f"Cancer Type: {model.cancer_type}")
            
        if model.cancer_subtype:
            context_parts.append(f"Cancer Subtype: {model.cancer_subtype}")
            
        if model.reasoning_for_inclusion:
            context_parts.append(f"Model Selection Rationale: {model.reasoning_for_inclusion}")
            
        # Include key citations
        if model.citations:
            key_citations = model.citations[:2]
            context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
        
        model_contexts.append(" | ".join(context_parts))
    
    # Verify contexts are properly created
    assert len(model_contexts) == 2
    assert len(model_names) == 2
    
    # Check protein model context
    protein_context = model_contexts[0]
    assert "His-tagged HER2-ECD protein" in protein_context
    assert "Protein-based binding assay" in protein_context
    assert "binding affinity measurements" in protein_context
    assert "ELISA using HER2 ECD protein" in protein_context
    
    # Check cell model context
    cell_context = model_contexts[1]
    assert "SK-BR-3" in cell_context
    assert "Breast Cancer" in cell_context
    assert "HER2-positive breast cancer" in cell_context
    assert "ADCC activity" in cell_context
    
    # Verify they are distinct
    assert protein_context != cell_context


def test_endpoint_model_association_logic():
    """Test the logic for associating endpoints with appropriate models."""
    
    # Define models and their appropriate endpoints
    protein_model = {
        "name": "His-tagged HER2-ECD protein",
        "type": "Protein-based binding assay",
        "appropriate_endpoints": ["ADC_KD", "ADC_EC50"],  # Binding affinity measurements
        "inappropriate_endpoints": ["ADC_IC50", "ADCC_ACTIVITY"]  # Cell-based measurements
    }
    
    cell_model = {
        "name": "SK-BR-3",
        "type": "Cell line",
        "appropriate_endpoints": ["ADC_IC50", "ADCC_ACTIVITY", "ANTIGEN_EXPRESSION"],  # Cell-based measurements
        "inappropriate_endpoints": ["ADC_KD"]  # Direct binding measurements
    }
    
    # Test the association logic
    def should_associate_endpoint_with_model(endpoint, model):
        """Logic to determine if an endpoint should be associated with a model."""
        if endpoint == "ADC_KD":
            # Binding affinity should be measured with protein-based systems
            return "Protein-based" in model["type"]
        elif endpoint in ["ADC_IC50", "ADCC_ACTIVITY"]:
            # Cell-based measurements should use cell lines
            return "Cell line" in model["type"] or "cell" in model["name"].lower()
        else:
            # Default association
            return True
    
    # Test protein model associations
    for endpoint in protein_model["appropriate_endpoints"]:
        assert should_associate_endpoint_with_model(endpoint, protein_model), \
            f"{endpoint} should be associated with {protein_model['name']}"
    
    for endpoint in protein_model["inappropriate_endpoints"]:
        assert not should_associate_endpoint_with_model(endpoint, protein_model), \
            f"{endpoint} should NOT be associated with {protein_model['name']}"
    
    # Test cell model associations
    for endpoint in cell_model["appropriate_endpoints"]:
        assert should_associate_endpoint_with_model(endpoint, cell_model), \
            f"{endpoint} should be associated with {cell_model['name']}"
    
    for endpoint in cell_model["inappropriate_endpoints"]:
        assert not should_associate_endpoint_with_model(endpoint, cell_model), \
            f"{endpoint} should NOT be associated with {cell_model['name']}"


def test_w2314772071_scenario():
    """Test the specific scenario from w2314772071.md paper."""
    
    # The correct association should be:
    # ADC_KD -> His-tagged HER2-ECD protein (binding assay)
    # ADC_IC50 -> SK-BR-3 (cell-based cytotoxicity)
    # ADCC_ACTIVITY -> SK-BR-3 (cell-based immune activity)
    
    models_and_endpoints = [
        {
            "model_name": "His-tagged HER2-ECD protein",
            "model_type": "Protein-based binding assay",
            "expected_endpoints": ["ADC_KD"],
            "reasoning": "Kd values determined by ELISA using HER2 ECD protein"
        },
        {
            "model_name": "SK-BR-3",
            "model_type": "Cell line",
            "expected_endpoints": ["ADC_IC50", "ADCC_ACTIVITY"],
            "reasoning": "Cell-based assays for cytotoxicity and immune activity"
        }
    ]
    
    # Verify the expected associations
    for model_info in models_and_endpoints:
        model_name = model_info["model_name"]
        model_type = model_info["model_type"]
        expected_endpoints = model_info["expected_endpoints"]
        
        for endpoint in expected_endpoints:
            if endpoint == "ADC_KD":
                # KD should be associated with protein-based binding assay
                assert "Protein-based" in model_type, \
                    f"ADC_KD should be measured using protein-based system, not {model_name}"
            elif endpoint in ["ADC_IC50", "ADCC_ACTIVITY"]:
                # Cell-based endpoints should be associated with cell lines
                assert "Cell line" in model_type or "cell" in model_name.lower(), \
                    f"{endpoint} should be measured using cell-based system, not {model_name}"
    
    print("✅ w2314772071.md scenario test passed!")
    print("   - ADC_KD correctly associated with His-tagged HER2-ECD protein")
    print("   - Cell-based endpoints correctly associated with SK-BR-3")


if __name__ == "__main__":
    # Run the tests
    test_protein_based_model_creation()
    test_enhanced_context_for_protein_models()
    test_endpoint_model_association_logic()
    test_w2314772071_scenario()
    print("All protein model extraction and enhanced context tests passed!")
