import pytest
import pandas as pd
import asyncio
import os
import sqlite3
from pathlib import Path
import pytest
import pandas as pd
import asyncio
import os
import sqlite3
from pathlib import Path
import sys

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from src.publication_profiler import PublicationProfilerAgent, setup_database

# Test data
SAMPLE_PUBLICATIONS = pd.DataFrame({
    'id': ['pub1'],
    'title': [
        "Preclinical Antitumor Activity of a Novel Anti-c-KIT Antibody-Drug Conjugate against Mutant and Wild-type c-KIT-Positive Solid Tumors",
    ],
    'abstract': [
        "Purpose: c-KIT overexpression is well recognized in cancers such as gastrointestinal stromal tumors (GIST), small cell lung cancer (SCLC), melanoma, non-small cell lung cancer (NSCLC), and acute myelogenous leukemia (AML). Treatment with the small-molecule inhibitors imatinib, sunitinib, and regorafenib resulted in resistance (c-KIT mutant tumors) or limited activity (c-KIT wild-type tumors). We selected an anti-c-KIT ADC approach to evaluate the anticancer activity in multiple disease models.Experimental Design: A humanized anti-c-KIT antibody LMJ729 was conjugated to the microtubule destabilizing maytansinoid, DM1, via a noncleavable linker (SMCC). The activity of the resulting ADC, LOP628, was evaluated in vitro against GIST, SCLC, and AML models and in vivo against GIST and SCLC models.Results: LOP628 exhibited potent antiproliferative activity on c-KIT-positive cell lines, whereas LMJ729 displayed little to no effect. At exposures predicted to be clinically achievable, LOP628 demonstrated single administration regressions or stasis in GIST and SCLC xenograft models in mice. LOP628 also displayed superior efficacy in an imatinib-resistant GIST model. Further, LOP628 was well tolerated in monkeys with an adequate therapeutic index several fold above efficacious exposures. Safety findings were consistent with the pharmacodynamic effect of neutropenia due to c-KIT-directed targeting. Additional toxicities were considered off-target and were consistent with DM1, such as effects in the liver and hematopoietic/lymphatic system.Conclusions: The preclinical findings suggest that the c-KIT-directed ADC may be a promising therapeutic for the treatment of mutant and wild-type c-KIT-positive cancers and supported the clinical evaluation of LOP628 in GIST, AML, and SCLC patients. Clin Cancer Res; 24(17); 4297-308. ©2018 AACR.",
    ]
})

@pytest.fixture
def test_db_path(tmp_path):
    """Create a temporary database for testing."""
    db_path = tmp_path / "test_publications.db"
    return str(db_path)

@pytest.fixture
def agent(test_db_path):
    """Create a PublicationProfilerAgent instance for testing."""
    return PublicationProfilerAgent(
        db_path=test_db_path,
        model_params={
            "deployment_name": "gpt-4o",
            "model_name": "gpt-4o",
            "max_retries": 1,
            "max_tokens": None,
            "temperature": 0.0,
            "top_p": 1.0,
        }
    )

@pytest.fixture
def db_connection(test_db_path):
    """Create a database connection for testing."""
    conn = sqlite3.connect(test_db_path)
    yield conn
    conn.close()

def test_database_setup(test_db_path):
    """Test that database and tables are created correctly."""
    conn = setup_database(test_db_path)
    cursor = conn.cursor()
    
    # Check if tables exist
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = {row[0] for row in cursor.fetchall()}
    
    assert "preclinical_results" in tables
    assert "indication_results" in tables
    assert "study_goal_results" in tables
    
    # Check table schemas
    cursor.execute("PRAGMA table_info(preclinical_results)")
    preclinical_columns = {row[1] for row in cursor.fetchall()}
    
    assert "id" in preclinical_columns
    assert "title" in preclinical_columns
    assert "abstract" in preclinical_columns
    assert "preclinical_reasoning" in preclinical_columns
    assert "status" in preclinical_columns
    
    conn.close()

def test_get_processed_publications(agent, db_connection):
    """Test retrieving processed publications."""
    cursor = db_connection.cursor()
    
    # Insert some test data
    cursor.execute('''
        INSERT INTO preclinical_results (id, title, abstract, status)
        VALUES (?, ?, ?, ?)
    ''', ('test1', 'Test Title', 'Test Abstract', 200))
    db_connection.commit()
    
    processed_ids = agent._get_processed_publications()
    assert 'test1' in processed_ids

@pytest.mark.asyncio
async def test_analyze_publication(agent):
    """Test analyzing a single publication."""
    result = await agent.analyze_publication(
        title=SAMPLE_PUBLICATIONS.iloc[0]['title'],
        abstract=SAMPLE_PUBLICATIONS.iloc[0]['abstract'],
        publication_id=SAMPLE_PUBLICATIONS.iloc[0]['id']
    )
    print("Analyze Publication Result:")
    print(result['result'].model_dump_json(indent=4))
    
    assert result['id'] == 'pub1'
    assert result['status'] in [200, 400]  # Either success or handled error
    if result['status'] == 200:
        assert result['result'] is not None
        assert hasattr(result['result'], 'adc_preclinical_assessment')
        assert hasattr(result['result'], 'indications')
        assert hasattr(result['result'], 'study_goals')

@pytest.mark.asyncio
async def test_analyze_dataframe(agent):
    """Test analyzing multiple publications in batches."""
    results = await agent.analyze_dataframe(SAMPLE_PUBLICATIONS, batch_size=2)
    
    assert len(results) == len(SAMPLE_PUBLICATIONS)
    for result in results:
        assert result['id'] in SAMPLE_PUBLICATIONS['id'].values
        assert result['status'] in [200, 400]

@pytest.mark.asyncio
async def test_duplicate_processing(agent):
    """Test that already processed publications are skipped."""
    # First run
    results1 = await agent.analyze_dataframe(SAMPLE_PUBLICATIONS.iloc[0:1], batch_size=1)
    assert len(results1) == 1
    
    # Second run with same data
    results2 = await agent.analyze_dataframe(SAMPLE_PUBLICATIONS.iloc[0:1], batch_size=1)
    assert len(results2) == 0  # Should skip already processed publication

@pytest.mark.asyncio
async def test_error_handling(agent):
    """Test handling of API errors."""
    # Create a publication with problematic content
    error_pub = pd.DataFrame({
        'id': ['error1'],
        'title': ['A' * 1000000],  # Very long title to potentially trigger API errors
        'abstract': ['B' * 1000000]  # Very long abstract
    })
    
    results = await agent.analyze_dataframe(error_pub, batch_size=1)
    assert len(results) == 1
    assert results[0]['id'] == 'error1'
    assert results[0]['status'] != 200  # Should indicate an error

def test_database_persistence(agent, db_connection):
    """Test that results are properly saved to database."""
    async def run_test():
        await agent.analyze_dataframe(SAMPLE_PUBLICATIONS.iloc[0:1], batch_size=1)
    
    asyncio.run(run_test())
    
    cursor = db_connection.cursor()
    
    # Check preclinical results
    cursor.execute("SELECT COUNT(*) FROM preclinical_results")
    assert cursor.fetchone()[0] > 0
    
    # Check related tables if successful analysis
    cursor.execute("SELECT status FROM preclinical_results WHERE id = ?", (SAMPLE_PUBLICATIONS.iloc[0]['id'],))
    status = cursor.fetchone()[0]
    
    if status == 200:
        cursor.execute("SELECT COUNT(*) FROM indication_results WHERE id = ?", (SAMPLE_PUBLICATIONS.iloc[0]['id'],))
        assert cursor.fetchone()[0] > 0
        
        cursor.execute("SELECT COUNT(*) FROM study_goal_results WHERE id = ?", (SAMPLE_PUBLICATIONS.iloc[0]['id'],))
        assert cursor.fetchone()[0] > 0

if __name__ == "__main__":
    pytest.main([__file__])