"""
Test for the enhanced model context functionality in endpoint extraction.
"""
import pytest
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.utils.extraction_pydantic_models import ExperimentalModel, AntibodyDrugConjugate
from unittest.mock import Mock, patch, AsyncMock
from jinja2 import Template


def test_model_context_creation():
    """Test that rich model context is properly created from ExperimentalModel objects."""
    
    # Create test models with different characteristics
    model1 = ExperimentalModel(
        citations=[
            "SK-BR-3 cells were transfected with HER2 ECD protein for overexpression studies.",
            "The modified SK-BR-3 cells showed 10-fold higher HER2 expression compared to parental cells."
        ],
        model_name="SK-BR-3-HER2-overexpressing",
        clinical_human_related=False,
        cancer_type="Breast Cancer",
        cancer_subtype="HER2-positive breast cancer",
        investigative=True,
        reasoning_for_inclusion="This genetically modified SK-BR-3 variant was selected for its extremely high HER2 expression levels, achieved through transfection with HER2 ECD protein, making it ideal for evaluating maximum binding capacity and cytotoxic potential of HER2-targeting ADCs."
    )
    
    model2 = ExperimentalModel(
        citations=[
            "JIMT-1 cells are naturally resistant to trastuzumab treatment.",
            "This cell line represents a clinically relevant model of HER2-positive resistant breast cancer."
        ],
        model_name="JIMT-1",
        clinical_human_related=False,
        cancer_type="Breast Cancer",
        cancer_subtype="Trastuzumab-resistant HER2-positive breast cancer",
        investigative=True,
        reasoning_for_inclusion="JIMT-1 was chosen to evaluate ADC efficacy in trastuzumab-resistant settings, representing a critical clinical challenge where conventional HER2-targeted therapies fail."
    )
    
    models = [model1, model2]
    
    # Simulate the context creation logic from extract_endpoints function
    model_contexts = []
    model_names = []
    
    for model in models:
        model_names.append(model.model_name)
        
        # Build comprehensive model context
        context_parts = [f"Model Name: {model.model_name}"]
        
        if model.cancer_type and model.cancer_type != "NONE":
            context_parts.append(f"Cancer Type: {model.cancer_type}")
            
        if model.cancer_subtype:
            context_parts.append(f"Cancer Subtype: {model.cancer_subtype}")
            
        if model.reasoning_for_inclusion:
            context_parts.append(f"Model Selection Rationale: {model.reasoning_for_inclusion}")
            
        # Include key citations that might contain experimental modifications
        if model.citations:
            # Take first 2 citations to avoid overwhelming the prompt
            key_citations = model.citations[:2]
            context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
        
        model_contexts.append(" | ".join(context_parts))
    
    # Verify the context creation
    assert len(model_contexts) == 2
    assert len(model_names) == 2
    
    # Check first model context
    context1 = model_contexts[0]
    assert "SK-BR-3-HER2-overexpressing" in context1
    assert "Breast Cancer" in context1
    assert "HER2-positive breast cancer" in context1
    assert "transfected with HER2 ECD protein" in context1
    assert "extremely high HER2 expression levels" in context1
    
    # Check second model context
    context2 = model_contexts[1]
    assert "JIMT-1" in context2
    assert "Trastuzumab-resistant" in context2
    assert "naturally resistant to trastuzumab" in context2
    assert "trastuzumab-resistant settings" in context2


def test_prompt_template_with_model_contexts():
    """Test that the endpoint extraction prompt template properly handles model contexts."""
    
    # Create sample data
    model_names = ["SK-BR-3-HER2-overexpressing", "JIMT-1"]
    model_contexts = [
        "Model Name: SK-BR-3-HER2-overexpressing | Cancer Type: Breast Cancer | Cancer Subtype: HER2-positive breast cancer | Model Selection Rationale: Selected for extremely high HER2 expression | Key Model Citations: SK-BR-3 cells were transfected with HER2 ECD protein",
        "Model Name: JIMT-1 | Cancer Type: Breast Cancer | Cancer Subtype: Trastuzumab-resistant HER2-positive breast cancer | Model Selection Rationale: Chosen to evaluate ADC efficacy in trastuzumab-resistant settings | Key Model Citations: JIMT-1 cells are naturally resistant to trastuzumab treatment"
    ]
    
    # Load and render the template
    template_content = """
The models to analyse:
{{MODELS}}

Detailed Model Context and Characteristics:
{% for context in MODEL_CONTEXTS %}
- {{context}}
{% endfor %}

ENHANCED CONTEXT AWARENESS: Use the detailed model context provided above to better understand:
- Specific genetic modifications or expressions (e.g., "HER2-overexpressing", "transfected with")
- Model variants and experimental characteristics
"""
    
    template = Template(template_content)
    rendered = template.render(
        MODELS=model_names,
        MODEL_CONTEXTS=model_contexts
    )
    
    # Verify the rendered template contains the expected information
    assert "SK-BR-3-HER2-overexpressing" in rendered
    assert "JIMT-1" in rendered
    assert "transfected with HER2 ECD protein" in rendered
    assert "naturally resistant to trastuzumab" in rendered
    assert "extremely high HER2 expression" in rendered
    assert "trastuzumab-resistant settings" in rendered
    
    # Verify the structure is correct
    assert "Detailed Model Context and Characteristics:" in rendered
    assert "ENHANCED CONTEXT AWARENESS:" in rendered


def test_model_context_edge_cases():
    """Test model context creation with edge cases (missing fields, empty values)."""
    
    # Model with minimal information
    minimal_model = ExperimentalModel(
        citations=["Basic citation"],
        model_name="BasicModel",
        clinical_human_related=False,
        cancer_type="NONE",  # Should be filtered out
        investigative=True,
        reasoning_for_inclusion="Basic reasoning"
    )
    
    # Model with no citations
    no_citations_model = ExperimentalModel(
        citations=[],
        model_name="NoCitationsModel",
        clinical_human_related=False,
        cancer_type="Test Cancer",
        investigative=True,
        reasoning_for_inclusion="Test reasoning"
    )
    
    models = [minimal_model, no_citations_model]
    
    # Create contexts
    model_contexts = []
    for model in models:
        context_parts = [f"Model Name: {model.model_name}"]
        
        if model.cancer_type and model.cancer_type != "NONE":
            context_parts.append(f"Cancer Type: {model.cancer_type}")
            
        if model.cancer_subtype:
            context_parts.append(f"Cancer Subtype: {model.cancer_subtype}")
            
        if model.reasoning_for_inclusion:
            context_parts.append(f"Model Selection Rationale: {model.reasoning_for_inclusion}")
            
        if model.citations:
            key_citations = model.citations[:2]
            context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
        
        model_contexts.append(" | ".join(context_parts))
    
    # Verify minimal model context (cancer_type "NONE" should be filtered out)
    context1 = model_contexts[0]
    assert "BasicModel" in context1
    assert "Cancer Type: NONE" not in context1  # Should be filtered out
    assert "Basic reasoning" in context1
    assert "Basic citation" in context1
    
    # Verify no citations model context
    context2 = model_contexts[1]
    assert "NoCitationsModel" in context2
    assert "Test Cancer" in context2
    assert "Test reasoning" in context2
    assert "Key Model Citations:" not in context2  # Should not appear when no citations


def test_context_length_management():
    """Test that model context properly handles citation length management."""
    
    # Model with many citations
    many_citations_model = ExperimentalModel(
        citations=[
            "Citation 1 with important experimental details",
            "Citation 2 with genetic modification information", 
            "Citation 3 with additional context",
            "Citation 4 with more details",
            "Citation 5 with even more information"
        ],
        model_name="ManyCitationsModel",
        clinical_human_related=False,
        cancer_type="Test Cancer",
        investigative=True,
        reasoning_for_inclusion="Test reasoning"
    )
    
    # Create context (should only include first 2 citations)
    context_parts = [f"Model Name: {many_citations_model.model_name}"]
    
    if many_citations_model.cancer_type and many_citations_model.cancer_type != "NONE":
        context_parts.append(f"Cancer Type: {many_citations_model.cancer_type}")
        
    if many_citations_model.reasoning_for_inclusion:
        context_parts.append(f"Model Selection Rationale: {many_citations_model.reasoning_for_inclusion}")
        
    if many_citations_model.citations:
        # Take first 2 citations to avoid overwhelming the prompt
        key_citations = many_citations_model.citations[:2]
        context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
    
    context = " | ".join(context_parts)
    
    # Verify only first 2 citations are included
    assert "Citation 1 with important experimental details" in context
    assert "Citation 2 with genetic modification information" in context
    assert "Citation 3 with additional context" not in context
    assert "Citation 4 with more details" not in context
    assert "Citation 5 with even more information" not in context


if __name__ == "__main__":
    # Run the tests
    test_model_context_creation()
    test_prompt_template_with_model_contexts()
    test_model_context_edge_cases()
    test_context_length_management()
    print("All enhanced model context tests passed!")
