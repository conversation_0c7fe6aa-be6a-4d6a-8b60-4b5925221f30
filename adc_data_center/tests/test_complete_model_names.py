"""
Test for complete model name preservation in model extraction.
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.utils.extraction_pydantic_models import ExperimentalModel


def test_complete_model_name_preservation():
    """Test that complete model names with full experimental context are preserved."""
    
    # Test cases representing the expected complete model names
    test_cases = [
        {
            "complete_name": "SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
            "simplified_name": "SK-BR-3",
            "context_elements": ["human", "breast adenocarcinoma", "cell line", "HER2-positive"]
        },
        {
            "complete_name": "His-tagged HER2-ECD protein (used in ELISA binding assay)",
            "simplified_name": "HER2 protein",
            "context_elements": ["His-tagged", "ECD", "ELISA", "binding assay"]
        },
        {
            "complete_name": "NCI-N87 xenograft model (HER2-positive, subcutaneous in female nude mice)",
            "simplified_name": "NCI-N87",
            "context_elements": ["xenograft model", "HER2-positive", "subcutaneous", "female nude mice"]
        },
        {
            "complete_name": "NIBIO G016 gastric cancer patient-derived xenograft (PDX) model (HER2 IHC 3+/FISH+)",
            "simplified_name": "NIBIO G016",
            "context_elements": ["gastric cancer", "patient-derived xenograft", "PDX", "HER2 IHC 3+", "FISH+"]
        },
        {
            "complete_name": "Cynomolgus monkeys (cross-reactive species for DS-8201a, used for pharmacokinetics and toxicity studies)",
            "simplified_name": "Cynomolgus monkeys",
            "context_elements": ["cross-reactive species", "DS-8201a", "pharmacokinetics", "toxicity studies"]
        }
    ]
    
    for test_case in test_cases:
        complete_name = test_case["complete_name"]
        simplified_name = test_case["simplified_name"]
        context_elements = test_case["context_elements"]
        
        # Create a model with the complete name
        model = ExperimentalModel(
            citations=[f"Test citation for {complete_name}"],
            model_name=complete_name,
            clinical_human_related=False,
            cancer_type="Test Cancer",
            investigative=True,
            reasoning_for_inclusion=f"Test reasoning for {complete_name}"
        )
        
        # Verify the complete name is preserved
        assert model.model_name == complete_name, \
            f"Expected complete name '{complete_name}', got '{model.model_name}'"
        
        # Verify the complete name is not the simplified version
        assert model.model_name != simplified_name, \
            f"Model name should not be simplified to '{simplified_name}'"
        
        # Verify all context elements are present in the complete name
        for element in context_elements:
            assert element in model.model_name, \
                f"Context element '{element}' missing from model name '{model.model_name}'"
        
        print(f"✅ Complete name preserved: {complete_name}")


def test_model_name_completeness_validation():
    """Test validation logic for model name completeness."""
    
    def is_complete_model_name(model_name):
        """Check if a model name includes sufficient experimental context."""
        # Simplified validation logic
        incomplete_indicators = [
            # Just cell line names without context
            model_name in ["SK-BR-3", "NCI-N87", "KPL-4", "JIMT-1"],
            # Just protein names without context
            model_name in ["HER2 protein", "ECD protein", "His protein"],
            # Just organism names without context
            model_name in ["mice", "rats", "monkeys"],
            # Very short names (likely incomplete)
            len(model_name.split()) < 3 and not any(char in model_name for char in ["(", "-", "+"]),
        ]
        
        return not any(incomplete_indicators)
    
    # Test complete names (should pass validation)
    complete_names = [
        "SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
        "His-tagged HER2-ECD protein (used in ELISA binding assay)",
        "NCI-N87 xenograft model (HER2-positive, subcutaneous in female nude mice)",
        "Cynomolgus monkeys (cross-reactive species for DS-8201a)",
        "NIBIO G016 gastric cancer patient-derived xenograft (PDX) model"
    ]
    
    for name in complete_names:
        assert is_complete_model_name(name), \
            f"Complete name '{name}' failed validation"
        print(f"✅ Complete name validated: {name}")
    
    # Test incomplete names (should fail validation)
    incomplete_names = [
        "SK-BR-3",
        "HER2 protein", 
        "NCI-N87",
        "mice",
        "monkeys",
        "ECD"
    ]
    
    for name in incomplete_names:
        assert not is_complete_model_name(name), \
            f"Incomplete name '{name}' incorrectly passed validation"
        print(f"✅ Incomplete name correctly rejected: {name}")


def test_enhanced_context_with_complete_names():
    """Test that enhanced model context works properly with complete model names."""
    
    # Create models with complete names
    models = [
        ExperimentalModel(
            citations=["Test citation for SK-BR-3 cell line"],
            model_name="SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
            clinical_human_related=False,
            cancer_type="Breast Cancer",
            cancer_subtype="HER2-positive breast cancer",
            investigative=True,
            reasoning_for_inclusion="Selected for high HER2 expression and established cytotoxicity assays"
        ),
        ExperimentalModel(
            citations=["Test citation for HER2 ECD protein"],
            model_name="His-tagged HER2-ECD protein (used in ELISA binding assay)",
            clinical_human_related=False,
            cancer_type="Protein-based binding assay",
            investigative=True,
            reasoning_for_inclusion="Used for direct binding affinity measurements without cellular interference"
        )
    ]
    
    # Simulate enhanced context creation
    model_contexts = []
    model_names = []
    
    for model in models:
        model_names.append(model.model_name)
        
        # Build comprehensive model context
        context_parts = [f"Model Name: {model.model_name}"]
        
        if model.cancer_type and model.cancer_type != "NONE":
            context_parts.append(f"Cancer Type: {model.cancer_type}")
            
        if model.cancer_subtype:
            context_parts.append(f"Cancer Subtype: {model.cancer_subtype}")
            
        if model.reasoning_for_inclusion:
            context_parts.append(f"Model Selection Rationale: {model.reasoning_for_inclusion}")
            
        if model.citations:
            key_citations = model.citations[:2]
            context_parts.append(f"Key Model Citations: {' | '.join(key_citations)}")
        
        model_contexts.append(" | ".join(context_parts))
    
    # Verify complete names are preserved in contexts
    assert len(model_contexts) == 2
    
    # Check first model context (cell line)
    cell_context = model_contexts[0]
    assert "SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)" in cell_context
    assert "Breast Cancer" in cell_context
    assert "HER2-positive breast cancer" in cell_context
    
    # Check second model context (protein)
    protein_context = model_contexts[1]
    assert "His-tagged HER2-ECD protein (used in ELISA binding assay)" in protein_context
    assert "Protein-based binding assay" in protein_context
    
    print("✅ Enhanced context preserves complete model names")
    print(f"   Cell context: {cell_context[:100]}...")
    print(f"   Protein context: {protein_context[:100]}...")


def test_prompt_template_compatibility():
    """Test that the updated prompts are compatible with complete model names."""
    
    # Simulate the model extraction prompt template variables
    complete_model_names = [
        "SK-BR-3 human breast adenocarcinoma cell line (HER2-positive)",
        "His-tagged HER2-ECD protein (used in ELISA binding assay)",
        "NCI-N87 xenograft model (HER2-positive, subcutaneous in female nude mice)"
    ]
    
    # Test that complete names work in template contexts
    for model_name in complete_model_names:
        # Verify the name contains sufficient context
        assert len(model_name) > 10, f"Model name too short: {model_name}"
        assert "(" in model_name or "-" in model_name, f"Model name lacks context markers: {model_name}"
        
        # Verify specific context elements are present
        if "SK-BR-3" in model_name:
            assert "human" in model_name and "breast" in model_name and "HER2" in model_name
        elif "HER2-ECD" in model_name:
            assert "His-tagged" in model_name and "ELISA" in model_name
        elif "NCI-N87" in model_name:
            assert "xenograft" in model_name and "mice" in model_name
    
    print("✅ Complete model names are compatible with prompt templates")


if __name__ == "__main__":
    # Run the tests
    test_complete_model_name_preservation()
    test_model_name_completeness_validation()
    test_enhanced_context_with_complete_names()
    test_prompt_template_compatibility()
    print("\n🎉 All complete model name tests passed!")
    print("   - Complete model names are properly preserved")
    print("   - Enhanced context works with complete names")
    print("   - Validation logic correctly identifies complete vs incomplete names")
    print("   - Prompt templates are compatible with complete model names")
