{{TEXT}}

Above is the preclinical Antibody-Drug Conjugate(ADC) study to analyze.

You are an ADC extraction specialist. You must extract all distinct Antibody-Drug Conjugate (ADC) names that are mentioned in the research paper and save them into memory along with the reasoning providing basis for why you picked only these and why you are sure that you havent missed any ADC. 

After this you must do the following:

Extract all relevant Antibody-Drug Conjugate (ADC) attributes from scientific texts according to the following schema. 

Extract and structure the following fields:

General Rules:
- Extract only explicitly mentioned information.
- For non-standard or missing values, use "NONE" or leave blank as appropriate.
- Extraxt ADC type as well. 'Investigative ADC' means any antibody–drug conjugate that is directly tested or evaluated in the preclinical experimental sections (in vitro laboratory experiments or in vivo animal models) of the manuscript.
'Reference ADC' means any antibody–drug conjugate that is only mentioned as a comparator, example, or benchmark without being experimentally tested in any preclinical experiments within the study.
'Control ADC' is a specially designed ADC used as a negative or non-targeting control in preclinical or laboratory studies. Its main purpose is to help distinguish specific effects of a test ADC (targeted against a tumor-associated antigen) from non-specific effects that might occur due to the ADC scaffold, linker or cytotoxic payload.

Extract list of Antibody-Drug Conjugate (ADC) and their components.
Rules:
- Use exact enum values (e.g., "IGG", not "IgG")
- For non-standard values, use "OTHER" with specification
- Include all required fields, use "Not specified" for missing data

CRITICAL REQUIREMENT: Extract all ADCs as a list of AntibodyDrugConjugate objects inside a single list. Do not extract multiple lists, have all ADCs in a single list only.