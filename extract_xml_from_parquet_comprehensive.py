#!/usr/bin/env python3
"""
Comprehensive XML extraction from parquet files based on specific work IDs.
This script searches through top_reviews.parquet, top_europepmc.parquet, and top_articles.parquet
for the specified work IDs and extracts their XML content.
"""

import pandas as pd
import os
from pathlib import Path
import json
from datetime import datetime

def analyze_parquet_structure(file_path):
    """Analyze the structure of a parquet file to understand its columns"""
    print(f"\n=== Analyzing {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return None, None, None
    
    try:
        df = pd.read_parquet(file_path)
        print(f"✅ Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        
        # Look for columns that might contain W IDs
        w_id_column = None
        xml_column = None
        
        print("\n   🔍 Searching for W ID and XML columns...")
        for col in df.columns:
            if df[col].dtype == 'object':
                sample_values = df[col].dropna().head(3).tolist()
                
                # Check if any values match W ID pattern
                w_id_matches = [val for val in sample_values if isinstance(val, str) and val.startswith('W') and val[1:].isdigit()]
                if w_id_matches and w_id_column is None:
                    w_id_column = col
                    print(f"   ✅ Found W ID column: '{col}' (sample: {w_id_matches[:2]})")
                
                # Check if column might contain XML (look for XML-like content)
                xml_matches = [val for val in sample_values if isinstance(val, str) and ('<' in val and '>' in val)]
                if xml_matches and xml_column is None and len(str(sample_values[0])) > 100:  # Likely XML if long and has tags
                    xml_column = col
                    print(f"   ✅ Found potential XML column: '{col}' (length: {len(str(sample_values[0]))})")
        
        if w_id_column is None:
            print("   ❌ No W ID column found!")
        if xml_column is None:
            print("   ❌ No XML column found!")
            
        return df, w_id_column, xml_column
        
    except Exception as e:
        print(f"❌ Error reading parquet file: {e}")
        return None, None, None

def extract_xml_for_work_ids(target_work_ids, output_dir="extracted_xml_comprehensive"):
    """Extract XML content for specified work IDs from all parquet files"""
    
    # Target work IDs from the user request
    print("="*80)
    print("COMPREHENSIVE XML EXTRACTION FROM PARQUET FILES")
    print("="*80)
    print(f"Target work IDs: {len(target_work_ids)} IDs")
    print(f"Output directory: {output_dir}")
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Parquet files to check
    parquet_files = [
        'adc_data_center/top_reviews.parquet',
        'adc_data_center/top_europepmc.parquet', 
        'adc_data_center/top_articles.parquet'
    ]
    
    # Track results
    extraction_results = {
        'timestamp': datetime.now().isoformat(),
        'target_work_ids': target_work_ids,
        'total_target_ids': len(target_work_ids),
        'files_processed': [],
        'found_ids': [],
        'missing_ids': [],
        'extracted_xml_files': [],
        'summary': {}
    }
    
    all_found_ids = set()
    
    # Process each parquet file
    for parquet_file in parquet_files:
        print(f"\n{'='*60}")
        print(f"PROCESSING: {parquet_file}")
        print(f"{'='*60}")
        
        df, w_id_col, xml_col = analyze_parquet_structure(parquet_file)
        
        file_result = {
            'file_path': parquet_file,
            'processed': False,
            'w_id_column': w_id_col,
            'xml_column': xml_col,
            'found_ids': [],
            'extracted_count': 0,
            'error': None
        }
        
        if df is None or w_id_col is None:
            file_result['error'] = 'Could not read file or find W ID column'
            extraction_results['files_processed'].append(file_result)
            continue
            
        file_result['processed'] = True
        
        # Search for target IDs in this file
        found_in_this_file = []
        for target_id in target_work_ids:
            if target_id in df[w_id_col].values:
                found_in_this_file.append(target_id)
                all_found_ids.add(target_id)
        
        file_result['found_ids'] = found_in_this_file
        print(f"\n   📊 Found {len(found_in_this_file)} target IDs in this file:")
        
        if found_in_this_file:
            for found_id in found_in_this_file:
                print(f"      ✅ {found_id}")
            
            # Extract XML content if XML column exists
            if xml_col:
                print(f"\n   📄 Extracting XML content from column '{xml_col}'...")
                
                for found_id in found_in_this_file:
                    try:
                        row = df[df[w_id_col] == found_id].iloc[0]
                        xml_content = row[xml_col]
                        
                        if pd.notna(xml_content) and str(xml_content).strip():
                            # Create filename with source info
                            source_name = Path(parquet_file).stem  # e.g., 'top_reviews'
                            xml_filename = f"{found_id}_{source_name}.xml"
                            xml_file_path = output_path / xml_filename
                            
                            with open(xml_file_path, 'w', encoding='utf-8') as f:
                                f.write(str(xml_content))
                            
                            print(f"      ✅ Saved: {xml_filename}")
                            extraction_results['extracted_xml_files'].append(str(xml_file_path))
                            file_result['extracted_count'] += 1
                        else:
                            print(f"      ❌ No XML content for: {found_id}")
                            
                    except Exception as e:
                        print(f"      ❌ Error extracting {found_id}: {e}")
            else:
                print(f"   ❌ No XML column found - cannot extract XML content")
        else:
            print("      (No target IDs found in this file)")
        
        extraction_results['files_processed'].append(file_result)
    
    # Calculate final results
    extraction_results['found_ids'] = list(all_found_ids)
    extraction_results['missing_ids'] = [id for id in target_work_ids if id not in all_found_ids]
    
    # Summary statistics
    extraction_results['summary'] = {
        'total_found': len(all_found_ids),
        'total_missing': len(extraction_results['missing_ids']),
        'success_rate': f"{len(all_found_ids)/len(target_work_ids)*100:.1f}%",
        'xml_files_created': len(extraction_results['extracted_xml_files'])
    }
    
    # Save extraction report
    report_file = output_path / 'extraction_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(extraction_results, f, indent=2, ensure_ascii=False)
    
    # Print final summary
    print(f"\n{'='*80}")
    print("EXTRACTION SUMMARY")
    print(f"{'='*80}")
    print(f"📊 Total target IDs: {len(target_work_ids)}")
    print(f"✅ Found IDs: {len(all_found_ids)} ({extraction_results['summary']['success_rate']})")
    print(f"❌ Missing IDs: {len(extraction_results['missing_ids'])}")
    print(f"📄 XML files created: {len(extraction_results['extracted_xml_files'])}")
    print(f"📁 Output directory: {output_path.absolute()}")
    print(f"📋 Detailed report: {report_file}")
    
    if extraction_results['missing_ids']:
        print(f"\n❌ Missing IDs:")
        for missing_id in extraction_results['missing_ids']:
            print(f"   - {missing_id}")
    
    if extraction_results['found_ids']:
        print(f"\n✅ Found IDs:")
        for found_id in extraction_results['found_ids']:
            print(f"   - {found_id}")
    
    return extraction_results

if __name__ == "__main__":
    # Target work IDs from user request
    target_work_ids = [
        'W2911746982', 'W3167756601', 'W2071163127', 'W2088635881', 'W2059265067',
        'W4210421364', 'W3083782960', 'W2800788936', 'W2987115036', 'W1670498114',
        'W2907078722', 'W4200625287', 'W2034382557', 'W3195418414', 'W3157341523',
        'W3043062793', 'W1491690127', 'W3177941998', 'W2894199580', 'W3135170551',
        'W4225380120', 'W4226170358', 'W4362561157', 'W4283659311', 'W4386861187',
        'W4226097966', 'W4376959447', 'W4312117329', 'W4388422409', 'W4387189752',
        'W4391224140', 'W4389113389', 'W4394615828', 'W4398201134', 'W4390885493',
        'W4392643092', 'W4400522799', 'W4393229268', 'W4394953855', 'W4391838941',
        'W4390743855', 'W4396807179', 'W4395049238', 'W4394883394', 'W4392047826',
        'W4392158917', 'W4390739402', 'W4396659145', 'W4392466679', 'W3182715110',
        'W2994610571', 'W3033351565', 'W2949179770', 'W3099316220', 'W2886837533',
        'W2767336874', 'W2150244588', 'W2898306454', 'W1983386052', 'W2891141728',
        'W2913433668', 'W2993509409', 'W2982651299', 'W2597720565', 'W2923147053',
        'W2755285110', 'W3152793228', 'W2767645122', 'W4396804283', 'W4406661909',
        'W4283165626', 'W4388771441', 'W4391276612', 'W4393015437', 'W3201179913',
        'W4387939270', 'W4390615633', 'W4401077310', 'W4293149970', 'W4386484293',
        'W4316814847', 'W4315631613', 'W4400235539', 'W4291202047', 'W4390577977',
        'W4381943608', 'W4385617285', 'W4220785729'
    ]
    
    # Run extraction
    results = extract_xml_for_work_ids(target_work_ids)
