#!/usr/bin/env python3
import os
import shutil
from pathlib import Path

def copy_xml_files():
    """Copy XML files to xml_of_interest directory with W ID names"""
    
    # Target W IDs to find
    target_w_ids = [
        'W4292181683', 'W2314772071', 'W4284897854', 'W4226170358',
        'W4226097966', 'W2064424529', 'W4400124600', 'W4393115738'
    ]
    
    # Source directory
    source_dir = Path('extractions updated/xml_files')
    
    # Destination directory
    dest_dir = Path('xml_of_interest')
    
    # Create destination directory
    dest_dir.mkdir(exist_ok=True)
    print(f"Created directory: {dest_dir}")
    
    # Track found and missing files
    found_files = []
    missing_files = []
    
    # Copy each target XML file
    for w_id in target_w_ids:
        source_file = source_dir / f"{w_id}.xml"
        dest_file = dest_dir / f"{w_id}.xml"
        
        if source_file.exists():
            shutil.copy2(source_file, dest_file)
            found_files.append(w_id)
            print(f"✓ Copied: {w_id}.xml")
        else:
            missing_files.append(w_id)
            print(f"✗ Missing: {w_id}.xml")
    
    # Summary
    print(f"\n=== SUMMARY ===")
    print(f"Total target files: {len(target_w_ids)}")
    print(f"Files found and copied: {len(found_files)}")
    print(f"Files missing: {len(missing_files)}")
    
    if found_files:
        print(f"\nSuccessfully copied files:")
        for w_id in found_files:
            print(f"  - {w_id}.xml")
    
    if missing_files:
        print(f"\nMissing files (not found in source directory):")
        for w_id in missing_files:
            print(f"  - {w_id}.xml")
    
    # Verify the destination directory
    print(f"\n=== VERIFICATION ===")
    copied_files = list(dest_dir.glob("*.xml"))
    print(f"Files in {dest_dir}:")
    for file in sorted(copied_files):
        print(f"  - {file.name}")

if __name__ == "__main__":
    copy_xml_files()
